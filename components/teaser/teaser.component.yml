# This is so your IDE knows about the syntax for fixes and autocomplete.
$schema: https://git.drupalcode.org/project/drupal/-/raw/HEAD/core/assets/schemas/v1/metadata.schema.json

# The human readable name.
name: Teaser

# Status can be: "experimental", "stable", "deprecated", "obsolete".
status: stable

# Schema for the props. We support www.json-schema.org. Learn more about the
# syntax there.
props:
  type: object
  properties:
    attributes:
      type: Drupal\Core\Template\Attribute
      title: Attributes
      description: Wrapper attributes.

# Slots always hold arbitrary markup. We know that beforehand, so no need for
# a schema for slots.
slots:
  # The key is the name of the slot. In your template you will use
  # {% block content %}.
  content:
    title: Content
    required: true
    description: The teaser content
  image:
    title: Image
    required: false
    description: Teaser image
  meta:
    title: Meta
    required: false
    description: Teaser meta
  prefix:
    title: Prefix
    required: false
    description: Contextual links slot
  title:
    title: Title content
    required: false
    description: Teaser title
