<?php

namespace Drupal\npx_test\Form;

use <PERSON>upal\Core\Entity\ContentEntityForm;
use Drupal\Core\Form\FormStateInterface;

/**
 * Form controller for Npx test answer edit forms.
 *
 * @ingroup npx_test
 */
class NpxTestAnswerForm extends ContentEntityForm {

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    /* @var $entity \Drupal\npx_test\Entity\NpxTestAnswer */
    $form = parent::buildForm($form, $form_state);

    $entity = $this->entity;

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function save(array $form, FormStateInterface $form_state) {
    $entity = $this->entity;

    $status = parent::save($form, $form_state);

    switch ($status) {
      case SAVED_NEW:
        drupal_set_message($this->t('Created the %label Npx test answer.', [
          '%label' => $entity->label(),
        ]));
        break;

      default:
        drupal_set_message($this->t('Saved the %label Npx test answer.', [
          '%label' => $entity->label(),
        ]));
    }
    $form_state->setRedirect('entity.npxtestanswer.canonical', ['npxtestanswer' => $entity->id()]);
  }

}
