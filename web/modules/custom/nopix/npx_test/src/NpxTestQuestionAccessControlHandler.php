<?php

namespace Drupal\npx_test;

use <PERSON><PERSON>al\Core\Entity\EntityAccessControlHandler;
use <PERSON>upal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Access\AccessResult;

/**
 * Access controller for the Npx test question entity.
 *
 * @see \Drupal\npx_test\Entity\NpxTestQuestion.
 */
class NpxTestQuestionAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    /** @var \Drupal\npx_test\Entity\NpxTestQuestionInterface $entity */
    switch ($operation) {
      case 'view':
        if (!$entity->isPublished()) {
          return AccessResult::allowedIfHasPermission($account, 'view unpublished npx test question entities');
        }
        return AccessResult::allowedIfHasPermission($account, 'view published npx test question entities');

      case 'update':
        return AccessResult::allowedIfHasPermission($account, 'edit npx test question entities');

      case 'delete':
        return AccessResult::allowedIfHasPermission($account, 'delete npx test question entities');
    }

    // Unknown operation, no opinion.
    return AccessResult::neutral();
  }

  /**
   * {@inheritdoc}
   */
  protected function checkCreateAccess(AccountInterface $account, array $context, $entity_bundle = NULL) {
    return AccessResult::allowedIfHasPermission($account, 'add npx test question entities');
  }

}
