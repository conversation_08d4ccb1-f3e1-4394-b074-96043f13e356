npx_survey.admin_config_entities:
  route_name: npx_survey.mainsection
  parent: system.admin_structure
  title: Npx Ankieta
  description: 'Ankie<PERSON> p<PERSON>zkoleniowa, narzędzia'
  weight: -10

# Npx survey entity menu items definition
entity.npx_survey_entity.collection:
  title: 'Npx survey entity list'
  route_name: entity.npx_survey_entity.collection
  description: 'List Npx survey entity entities'
  parent: npx_survey.admin_config_entities
  weight: 100

npx_survey_entity.admin.structure.settings:
  title: 'Npx survey entity settings'
  description: 'Configure Npx survey entity entities'
  route_name: npx_survey_entity.settings
  parent: npx_survey.admin_config_entities
