<?php

namespace Drupal\npx_blocks\Plugin\Block;

use <PERSON><PERSON><PERSON>\paragraphs\Entity\Paragraph;
use <PERSON><PERSON>al\Core\Url;
use <PERSON><PERSON>al\Core\Block\BlockBase;
use Dr<PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON>al\Core\Routing\CurrentRouteMatch;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;


/**
 * Provides a 'BreadcrumbBlock' block.
 *
 * @Block(
 *  id = "npx_breadcrumb_block",
 *  admin_label = @Translation("Npx Breadcrumb block"),
 * )
 */
class BreadcrumbBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * \Drupal\Core\Entity\EntityTypeManager definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;
  
  /**
   * Drupal\Core\Routing\CurrentRouteMatch definition.
   *
   * @var \Drupal\Core\Routing\CurrentRouteMatch
   */
  protected $currentRouteMatch;
  
  /**
   * Constructs a new BreadcrumbBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
    CurrentRouteMatch $current_route_match
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->currentRouteMatch = $current_route_match;
    $this->entityTypeManager = $entity_type_manager;
  }
  
  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('current_route_match')
    );
  }

  /**
   * Checks if a URL path leads to a published node.
   *
   * @param string $path
   *   The internal path to check (e.g. '/biblioteka-biznesu').
   *
   * @return bool
   *   TRUE if the path leads to a published node or accessible view.
   */
  private function isPublishedPath($path) {
    if (!$path) {
      return false;
    }
  
    $path_alias = \Drupal::service('path_alias.manager')->getPathByAlias($path);
    
    // Check if it's a node path
    if (preg_match('/node\/(\d+)/', $path_alias, $matches)) {
      $nid = $matches[1];
      $node = $this->entityTypeManager->getStorage('node')->load($nid);
      return $node && $node->isPublished();
    }
    // For non-node paths (like views), check if URL is valid and accessible
    try {
      if ($url = \Drupal::service('path.validator')->getUrlIfValid($path)) {
        return $url->access(\Drupal::currentUser());
      }
    }
    catch (\Exception $e) {
      \Drupal::logger('npx_blocks')->error('Error checking path access: @error', ['@error' => $e->getMessage()]);
    }
    
    return false;
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build = [];
    
    $links = [];
    
    $links[] = [
      'title' => $this->t('Home'),
      'url' => Url::fromRoute('<front>'),
    ];


    $build['#attached'] = [
      'library' => [
        'npx_blocks/npx_blocks.breadcrumb_block'
      ]
    ];

  // Check if we're on a view page
  $view_id = $this->currentRouteMatch->getParameter('view_id');
  $display_id = $this->currentRouteMatch->getParameter('display_id');
  

  if ($view_id) {
    $view = $this->entityTypeManager->getStorage('view')->load($view_id);
    if ($view) {
      $executable = $view->getExecutable();
      $executable->setDisplay($display_id);
      $title = $executable->getTitle();
      
      $links[] = [
        'title' => $title,
      ];
      
      $build['breadcrumb_block'] = [
        '#theme' => 'links',
        '#links' => $links,
        '#attributes' => [
          'class' => ['n-breadcrumb', 'mt-3', 'inner', 'd-block'],
        ],
        '#cache' => [
          'contexts' => ['url.path', 'route'],
          'tags' => ['config:views.view.' . $view_id],
        ],
      ];
      
      return $build;
    }
  }

    $node = $this->currentRouteMatch->getParameter('node');
    
    if(!$node instanceof NodeInterface) {
      return $build;
    }

    if ($node->bundle() == 'landing_page' || $node->bundle() == 'npxtraining') { 
      $szkolenia_node = $this->entityTypeManager->getStorage('node')->load(588);
      if ($szkolenia_node && $szkolenia_node->isPublished()) {
        $links[] = [
          'title' => $this->t('Szkolenia'),
          'url' => Url::fromRoute('entity.node.canonical', ['node' => 588]),
        ];
      }
    } else if ($node->bundle() == 'opinia') {
      if ($this->isPublishedPath('/opinie')) {
        $links[] = [
          'title' => $this->t('Opinie'),
          'url' => Url::fromUri('internal:/opinie'),
        ];
      }
      $links[] = [
        'title' => $node->getTitle(),
      ];
    } else if($node->bundle() == 'article' || $node->bundle() == 'media' || $node->bundle() == 'blog_cat_page') {
      if ($this->isPublishedPath('/blog')) {
        $links[] = [
          'title' => $this->t('Blog'),
          'url' => Url::fromUri('internal:/blog'),
        ];
      }
      $links[] = [
        'title' => $node->getTitle(),
      ];
    } else if($node->bundle()=='recenzja') {
      if ($this->isPublishedPath('/biblioteka-biznesu')) {
        $links[] = [
          'title' => $this->t('Biblioteka biznesu'),
          'url' => Url::fromUri('internal:/biblioteka-biznesu'),
        ];
      }
      $links[] = [
        'title' => $node->getTitle(),
      ];
    } else if($node->bundle() == 'page' || $node->bundle() == 'webform' || $node->bundle() == 'npxtraining_date' || $node->bundle() == 'book' || $node->bundle() == 'npxtrainer' || $node->bundle() == 'coaching' || $node->bundle() == 'followup' || $node->bundle() == 'npxquiz' || $node->bundle() == 'referencja' || $node->bundle() == 'strona_opinii') {
      $links[] = [
        'title' => $node->getTitle(),
      ];
    }
    if ($node->hasField('field_npxtraining_category') && $node->get('field_npxtraining_category')->entity) {
      $term = $node->get('field_npxtraining_category')->entity;
      
      if($term->get('field_npx_desc_node')->entity) {
        $desc_node = $term->get('field_npx_desc_node')->entity;
        
        $links[] = [
          'title' => $desc_node->getTitle(),
          'url' => Url::fromRoute('entity.node.canonical', ['node' => $desc_node->id()]),
        ];
      } else {
        $links[] = [
          'title' => $term->get('name')->value,
          'url' => Url::fromRoute('entity.taxonomy_term.canonical', ['taxonomy_term' => $term->id()]),
        ];
      }
      
      /** @var \Drupal\paragraphs\Entity\Paragraph $top_title_paragraph */
      $top_title_paragraph = reset($node->get('field_top_tytul')->referencedEntities());
      
      if($top_title_paragraph instanceof Paragraph) {
        $links[] = [
          'title' => $top_title_paragraph->get('field_ts_tytul')->value,
        ];
      }
    }
    if ($node->bundle() == 'landing_page') {
      $links[] = [
        'title' => $node->getTitle(),
      ];
    }
    $build['breadcrumb_block'] = [
      '#theme' => 'links',
      '#links' => $links,
      '#attributes' => [
        'class' => ['n-breadcrumb', 'mt-3', 'inner', 'd-block'],
      ],
    ];

    return $build;
  }

}
