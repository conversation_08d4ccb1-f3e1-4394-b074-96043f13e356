import { Plugin, Command } from "ckeditor5/src/core";
import { Collection } from "ckeditor5/src/utils";
import { addListToDropdown, createDropdown, Model } from "ckeditor5/src/ui";

class InsertDivCommand extends Command {
  execute({ value }) {
    const editor = this.editor;
    const model = editor.model;

    model.change((writer) => {
      const selection = model.document.selection;

      if (selection.isCollapsed) {
        return;
      }

      const range = selection.getFirstRange();

      if (!range) {
        return;
      }

      const selectedText = Array.from(range.getItems())
        .map((item) => item.data)
        .join("");

      const parentElement = range.start.parent;

      if (!parentElement || parentElement.name !== "paragraph") {
        return;
      }

      const div = writer.createElement("div", { divClass: value });
      const textNode = writer.createText(selectedText);
      writer.append(textNode, div);

      writer.remove(range);
      if (parentElement.positionBefore) {
        writer.insert(div, parentElement.positionBefore);
        writer.remove(parentElement);
      } else {
        writer.insert(div, range.start);
      }

      writer.setSelection(div, "after");
    });
  }

  refresh() {
    const model = this.editor.model;
    const selection = model.document.selection;

    this.isEnabled = !selection.isCollapsed;
  }
}

export default class StyleBlockDropdownPlugin extends Plugin {
  init() {
    const editor = this.editor;
    const t = editor.t;

    editor.model.schema.extend("$block", { allowIn: "div" });
    editor.model.schema.register("div", {
      isBlock: true,
      allowWhere: "$block",
      allowContentOf: "$block",
    });
    editor.model.schema.extend("paragraph", { allowIn: "div" });
    editor.model.schema.extend("paragraph", { allowChildren: ["div"] });
    editor.model.schema.extend("$root", { allowIn: "div" });
    editor.model.schema.extend("$root", { allowChildren: "div" });

    editor.conversion.for("upcast").elementToElement({
      view: "div",
      model: "div",
    });
    editor.conversion.for("dataDowncast").elementToElement({
      model: "div",
      view: (modelElement, { writer }) => {
        return writer.createContainerElement("div", {
          class: modelElement.getAttribute("divClass"),
        });
      },
    });

    editor.conversion.for("editingDowncast").elementToElement({
      model: "div",
      view: (modelElement, { writer }) => {
        return writer.createContainerElement("div", {
          class: modelElement.getAttribute("divClass"),
        });
      },
    });

    editor.commands.add("insertDiv", new InsertDivCommand(editor));

    editor.ui.componentFactory.add("myDropdownBlock", (locale) => {
      const dropdownView = createDropdown(locale);

      addListToDropdown(dropdownView, this._createDropdownItems());

      dropdownView.buttonView.set({
        label: t("Style blokowe"),
        tooltip: true,
        withText: true,
      });

      this.listenTo(dropdownView, "execute", (evt) => {
        editor.execute("insertDiv", { value: evt.source.commandParam });
        editor.editing.view.focus();
      });

      return dropdownView;
    });

    let toolbar = editor.config.get("toolbar");

    if (Array.isArray(toolbar)) {
      if (toolbar.indexOf("myDropdownBlock") === -1) {
        editor.config.set("toolbar", [...toolbar, "myDropdownBlock"]);
      }
    } else if (typeof toolbar === "string") {
      toolbar = toolbar.split(",");
      if (toolbar.indexOf("myDropdownBlock") === -1) {
        editor.config.set("toolbar", toolbar.join(",") + ",myDropdownBlock");
      }
    } else if (typeof toolbar === "object" && Array.isArray(toolbar.items)) {
      if (toolbar.items.indexOf("myDropdownBlock") === -1) {
        toolbar.items.push("myDropdownBlock");
        editor.config.set("toolbar", toolbar);
      }
    }
  }

  _createDropdownItems() {
    const itemDefinitions = new Collection();

    itemDefinitions.add({
      type: "button",
      model: new Model({
        commandParam: "npx-form-button",
        label: "Link do szkolenia",
        withText: true,
      }),
    });
    itemDefinitions.add({
      type: "button",
      model: new Model({
        commandParam: "npx-program-button",
        label: "Link do programu",
        withText: true,
      }),
    });
    itemDefinitions.add({
      type: "button",
      model: new Model({
        withText: true,
        label: "NLink do programu",
        commandParam: "npx-program-button blue",
      }),
    });
    itemDefinitions.add({
      type: "button",
      model: new Model({
        withText: true,
        label: "Ukryty tekst",
        commandParam: "npx-hidden-text",
      }),
    });

    return itemDefinitions;
  }
}
