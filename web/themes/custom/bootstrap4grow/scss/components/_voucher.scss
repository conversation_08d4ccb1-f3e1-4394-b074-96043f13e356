.npx-voucher-wrapper {
  position: fixed;
  top: 100px;
  right: 0;
  display: none;
  z-index: 999;
}

a.npx-close-voucher-block, a.npx-close-voucher-second-block {
  top: 13px;
}
.npx-close-voucher-block, .npx-close-voucher-second-block {
  position: absolute;
  right: 0;
  top: 0;
  width: 32px;
  height: 32px;
  opacity: .7;
  padding: 10px;
  &:before, &:after {
    position: absolute;
    left: 15px;
    content: ' ';
    height: 16px;
    width: 2px;
    background-color: #333;
    box-shadow: 0 0 10px 2px #fff;
  }
  &:before {
    transform: rotate(45deg);
  }
  &:after {
    transform: rotate(-45deg);
  }
}
.block-voucher-second-block {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.4);
  display: none;
  z-index: 999999;
}
.npx-voucher-second-wrapper {
  position: fixed;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  img {
    max-height: calc( 100vh - 100px );
    width: auto;
  }
}
