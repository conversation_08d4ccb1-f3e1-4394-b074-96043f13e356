.opinia {
  .group-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-image: url("../images/opinia.png");

    .webp & {
      background-image: url("../images/opinia.webp");
    }
    background-position: right top;
    background-repeat: no-repeat;
    padding-top: spacer(6);
    img {
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      border-radius: 50%;
      box-shadow: 1px 1px 6px 0 #a1a1a1;
    }
  }
  .field--name-field-linkedin-link {
    display: flex;
    align-self: flex-end;
    a {
      position: relative;
      text-indent: -99999px;
      text-align: left;
      display: block;
      width: 46px;
      height: 11px;
      margin-top: 5px;
      background-size: contain;
      background-repeat: no-repeat;
      align-self: end;
    }
    a::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("../images/linkedin-logo.png");
      filter: grayscale(100%);
    }
  }
  .n-signature-wrapper {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-content: flex-end;
    margin-right: spacer(5);
    p {
      margin: 0;
      text-align: right;
    }
  }
  .field--name-field-image {
    margin-left: 0;
    padding-right: 6px;
  }
  .field--name-field-zajawka p {
    font-size: 12px;
    font-weight: bold;
    line-height: 19px;
  }
}
