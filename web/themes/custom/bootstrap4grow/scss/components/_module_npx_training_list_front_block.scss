[id^=block-bootstrap4grow-traininglistfrontblock] {
  padding: 0 25px 25px;
  border: 3px solid #f07925;
  max-width: 300px;
  @include media-breakpoint-up(sm) {
    max-width: 450px;
  }
  @include media-breakpoint-up(md) {
    max-width: 720px;
  }
  @include media-breakpoint-up(lg) {
    max-width: 960px;
  }
  @include media-breakpoint-up(xl) {
    max-width: 1200px;
  }
  &.content > div::after {
    clear: both;
    content: ".";
    display: block;
    height: 0;
    visibility: hidden;
  }
  & > div:not(.fakeheader) {
    @include media-breakpoint-up(md) {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
    }
  }
  h3 {
    color: #f07925;
    width: 100%;
    text-align: center;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 44px;
    margin: 35px 0 0;
  }
  .npx-city {
    font-weight: 600;
    font-size: 20px;
  }
  .npx-closest {
    font-weight: 600;
    font-size: 35px;
  }
  .npx-row {
    margin-top: spacer(6);
    padding: 5px;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    outline: 0 #f07925 solid;
    transition: .1s all;
    @include media-breakpoint-up(md) {
      max-width: 47%;
      flex: 1 0 47%;
    }
    @include media-breakpoint-up(lg) {
      max-width: 25%;
      flex: 1 0 25%;
    }
    &:hover, &:focus {
      outline: 3px #f07925 solid;
      cursor: pointer;
    }
    &-inner {
      display: flex;
      flex-direction: column;
      background-size: cover;
      background-image: none;
      background-color: #1e3850;
      color: #fff;
      text-align: center;
      padding: 0;
      height: 260px;
      -moz-box-sizing: border-box;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      position: relative;
      margin-bottom: 80px;
      .wrapper {
        background-image: url(../images/szkolenie-tlo.png);

    .webp & {
      background-image: url(../images/szkolenie-tlo.webp);
    }

    .webp & {
      background-image: url(../images/szkolenie-tlo.webp);
    }
        background-repeat: none;
        background-size: cover;
        margin-top: 260px;
        height: auto;
        padding: spacer(3);
        font-size: 18px;
        position: absolute;
        bottom: 0;
        width: 100%;
	@include media-breakpoint-up(md) {
          height: 130px;
          padding: spacer(6);
        }
        @include media-breakpoint-up(lg) {
          padding: spacer(12px 4);
        }
        @include media-breakpoint-up(xl) {
          padding: spacer(24px 4);
        }
      }
      .post-wrapper {
        position: absolute;
        bottom: -88px;
        width: 100%;
        padding: 15px 0;
        color: #f07925;
        margin-bottom: -16px;
      }
    }
  }
}
