.sekcja.wyznaczamy-standardy, body.node--type-page {
  .npx-form-button-wrapper {
    text-align: center;
  }
  a.npx-form-button.npx-autolink, a.npx-form-button-inline.npx-autolink {
    margin-top: 0;
  }
  .field--name-field-extra-tekst-g3 p.npx-hidden-text {
    font-size: $p-font-size;
    font-weight: 300;
  }
  .field--name-field-wideo-trenera-poster {
    width: 100%;
    height: 100px;
    @include media-breakpoint-up(sm) {
      width: 128px;
      height: 128px;
    }
    @include media-breakpoint-up(lg) {
      width: 182px;
      height: 182px;
    }
    img {
      max-width: 100px;
      display: inline-block;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      border-radius: 50%;
      -moz-box-shadow: 1px 1px 40px #ebedec;
      -webkit-box-shadow: 1px 1px 40px #ebedec;
      box-shadow: 1px 1px 40px #ebedec;
      @include media-breakpoint-up(sm) {
        display: inherit;
        max-width: 128px;
      }
      @include media-breakpoint-up(lg) {
        max-width: 100%;
      }
    }
  }
  .field--name-field-cechy {
    &::before, &::after {
      position: absolute;
      content: " ";
      display: block;
      left: -20px;
      top: 0;
      width: calc(100% + 40px);
      height: 100%;
    }
    &::before {
      z-index: 0;
      background: transparent url(../images/wyznaczamy-standardy-1.png) repeat-y 0 0;

      .webp & {
        background: transparent url(../images/wyznaczamy-standardy-1.webp) repeat-y 0 0;
      }
      background-size: 150% auto;
      @include media-breakpoint-down(xl) {
        background-position: -100px 0;
        @include full-width;
        position: absolute;
      }
      @include media-breakpoint-up(xl) {
        background-size: 100% auto;
      }
    }
    &::after {
      z-index: 1;
      background: transparent url("../images/wyznaczamy-standardy-2.png") repeat-x left bottom;

      .webp & {
        background: transparent url("../images/wyznaczamy-standardy-2.webp") repeat-x left bottom;
      }
    }
    .field__item {
      z-index: 10;
      clear: both;
      margin-bottom: 25px;
      &.field--name-field-opis-trenera {
        margin-bottom: 50px;
        @include media-breakpoint-up(sm) {
          margin-bottom: 25px;
        }
      }
    }
  }
  .paragraph--type-cecha-par {
    max-width: 900px;
  }
}
.field_cechy_field_item_even .paragraph--type-cecha-par {
  margin-left: auto;
}
.paragraph--type-cecha-par .field--name-field-opis-trenera {
  width: 100%;
  padding: 0;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 50px;
  @include media-breakpoint-up(sm) {
    width: calc(100% - 128px);
    padding: 0 0 0 50px;
  }
  @include media-breakpoint-up(lg) {
    width: calc(100% - 182px);
    padding: 0 0 0 100px;
  }
  p {
    display: inline-block;
  }
  h3, summary {
    position: relative;
    font-size: 22.4px;
    font-weight: 600;
    line-height: 26px;
    text-align: center;
    @include media-breakpoint-up(sm) {
      margin-left: -45px;
      text-align: left;
      margin-bottom: 0;
    }
    @include media-breakpoint-up(lg) {
      margin-left: -80px;
    }
    sup {
      display: block;
      max-width: 100px;
      margin: spacer(6 auto);
      padding: spacer(0 2);
      vertical-align: middle;
      line-height: 20px;
      font-size: 10px;
      color: #fff;
      background-color: #f73965;
      position: relative;
      height: 20px;
      @include media-breakpoint-up(sm) {
        display: inline-block;
        margin-top: 0;
        margin-left: 5px;
        vertical-align: sub;
      }
    }
  }
}
a.benefits-linker {
  font-weight: bold;
  line-height: 31px;
  color: #000;
  &:hover, &:active {
    color: #000;
  }
  @include media-breakpoint-up(sm) {
    margin-left: -45px;
  }
  @include media-breakpoint-up(lg) {
    margin-left: -80px;
  }
}
