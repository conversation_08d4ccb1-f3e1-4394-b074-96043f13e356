body .slider {
  height: 703px;
}
body .slider .owl-item {
  background: none!important;
  @include media-breakpoint-up(md) {
    background: url("../images/slider2-2.webp")!important;
    background-repeat: no-repeat;
    background-size: cover !important;
    background-position: left -125px center !important;
  }
  .views-field-body {
    margin-left: auto;
    margin-right: auto;
    width: 96%;
    @include media-breakpoint-up(sm) {
      width: 1200px;
      max-width: 100%;
    }
    .field-content {
      width: 100%;
      @include media-breakpoint-up(sm) {
        width: auto;
      }
    }
    .cytat {
      position: absolute;
      @include media-breakpoint-up(lg) {
        right: 75px;
        top: 25px;
        width: 30%;
      }
      @include media-breakpoint-up(xl) {
        right: 55px;
        top: -25px;
      }
      @include media-breakpoint-up(xla) {
        right: 245px;
      }
      img {
        display: none;
	@include media-breakpoint-up(lg) {
          display: block;
        }
      }
    }
    .tekst {
      max-width: 450px;
      float: left;
      @include media-breakpoint-up(sm) {
        max-width: 100%;
        padding: spacer(0 3);
      }
      @include media-breakpoint-up(md) {
        max-width: 450px;
      }
      em.title {
        font-size: 66px;
        font-weight: 100;
        text-align: center;
        line-height: 62px;
        text-transform: uppercase;
        padding-top: 100px;
        margin-bottom: spacer(4);
        float: left;
        font-style: normal;
        color: #fff;
      }
      p, h2 {
        color: #fff;
        font-size: 20px;
        font-weight: 700;
        text-align: center;
        padding-top: spacer(2);
        padding-bottom: 60px;
      }
      a {
        @include npx-button;
	font-size: 18px;
        padding: 21px 25px;
      }
    }
  }
}
.frontcolor {
  color: #fff;
}
