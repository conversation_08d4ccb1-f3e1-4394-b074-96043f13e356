<{{ wrapper_element }} {{ attributes }}>
  {% if title %}
    <{{ title_element }}{{ title_attributes }}>{{ title }}</{{ title_element }}>
  {% endif %}

  {% if collapsible %}
    <div class="field-group-wrapper">
  {% endif %}

  {% if video_embed_rendered is not empty and video_text_rendered is not empty %}
    {# Render all fields except video_embed and video_text #}
    {% for key, field in content %}
      {% if key != 'field_video_embed' and key != 'field_video_text' %}
        {{ field }}
      {% endif %}
    {% endfor %}

    <div class="row">
      <div class="col-lg-4">
        {{ video_embed_rendered|raw }}
      </div>
      <div class="col-lg-8">
        {{ video_text_rendered|raw }}
      </div>
    </div>
  {% else %}
    {{ children }}
  {% endif %}

  {% if collapsible %}
    </div>
  {% endif %}
</{{ wrapper_element }}>