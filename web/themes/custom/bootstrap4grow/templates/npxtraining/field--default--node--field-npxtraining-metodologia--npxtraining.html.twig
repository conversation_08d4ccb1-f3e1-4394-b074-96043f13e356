{#
/**
 * @file
 * Theme override for a field.
 *
 * To override output, copy the "field.html.twig" from the templates directory
 * to your theme's directory and customize it, just like customizing other
 * Drupal templates such as page.html.twig or node.html.twig.
 *
 * Instead of overriding the theming for all fields, you can also just override
 * theming for a subset of fields using
 * @link themeable Theme hook suggestions. @endlink For example,
 * here are some theme hook suggestions that can be used for a field_foo field
 * on an article node type:
 * - field--node--field-foo--article.html.twig
 * - field--node--field-foo.html.twig
 * - field--node--article.html.twig
 * - field--field-foo.html.twig
 * - field--text-with-summary.html.twig
 * - field.html.twig
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - label_hidden: Whether to show the field label or not.
 * - title_attributes: HTML attributes for the title.
 * - label: The label for the field.
 * - multiple: TRUE if a field can contain multiple items.
 * - items: List of all the field items. Each item contains:
 *   - attributes: List of HTML attributes for each item.
 *   - content: The field item's content.
 * - entity_type: The entity type to which the field belongs.
 * - field_name: The name of the field.
 * - field_type: The type of the field.
 * - label_display: The display settings for the label.
 *
 *
 * @see template_preprocess_field()
 */
#}
{%
  set classes = [
    'field',
    'field--name-' ~ field_name|clean_class,
    'field--type-' ~ field_type|clean_class,
    'field--label-' ~ label_display,
  ]
%}
{%
  set title_classes = [
    'field__label',
    label_display == 'visually_hidden' ? 'visually-hidden',
  ]
%}

<div class="methodology-wrapper mb-7">
  <p>{{ "W naszych szkoleniach dostajesz właśnie w takich proporcjach:" | trans }}</p>
  <div class="methodology-inner-wrapper mt-7 d-xl-flex">
    <div class="methodology-image mb-7 mb-xl-0 mx-auto mx-xl-0 position-relative">
      <picture class="d-xl-none">
        <source data-srcset="/themes/custom/bootstrap4grow/images/metodyka-mobile.webp" type="image/webp">
        <img alt="metodyka mobile" width="419" height="422" class="b-lazy" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="/themes/custom/bootstrap4grow/images/metodyka-mobile.png" />
      </picture>
      <picture class="d-none d-xl-block">
        <source data-srcset="/themes/custom/bootstrap4grow/images/metodyka-desktop.webp" type="image/webp">
        <img alt="metodyka desktop" width="500" height="546" class="b-lazy" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="/themes/custom/bootstrap4grow/images/metodyka-desktop.png" />
      </picture>
    </div>
    <div class="methodology-items pt-5 pt-xl-0">
      <div class="methodology-item pb-4 position-relative d-xl-flex">
        <div class="methodology-icon mb-3 mb-xl-0">
          <picture>
            <source data-srcset="/themes/custom/bootstrap4grow/images/techniki.webp" type="image/webp" class="b-lazy">
            <img alt="techniki" class="b-lazy" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="100" height="100" data-src="/themes/custom/bootstrap4grow/images/techniki.png" />
          </picture>
        </div>
        <p class="methodology-item-text mb-xl-0 d-xl-flex flex-row">
          <span class="h5 text-black d-xl-flex align-items-center fw-bold methodology-item-title">{{ "Techniki i narzędzia" | trans }}</span>
          <span class="methodology-item-desc text-black d-xl-flex align-items-center">{{ "konkretne strategie działania adekwatne do sytuacji i optymalne narzędzia (np. formularze, scenariusze rozmów, matryce, ściągi) – gotowce, które wiesz jak wykorzystać na co dzień" | trans }}</span>
        </p>
      </div>
      <div class="methodology-item pb-4 position-relative d-xl-flex">
        <div class="methodology-icon mb-3 mb-xl-0">
          <img alt="warsztat" width="100" height="100" class="b-lazy" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="/themes/custom/bootstrap4grow/images/warsztat.png" />
        </div>
        <p class="methodology-item-text mb-xl-0 d-xl-flex flex-row">
          <span class="h5 text-black d-xl-flex align-items-center fw-bold methodology-item-title">{{ "Warsztat" | trans }}</span>
          <span class="methodology-item-desc text-black d-xl-flex align-items-center">{{ "dzięki ćwiczeniom grupowym, grom, pracy w parach, na gorącym krześle, prowokacjom, wdrażasz nowe techniki i narzędzia, doświadczasz omawianych mechanizmów na sobie także w zaskakujących eksperymentach" | trans }}</span>
        </p>
      </div>
      <div class="methodology-item pb-4 position-relative d-xl-flex">
        <div class="methodology-icon mb-3 mb-xl-0">
          <img alt="wiedza" width="100" height="100" class="b-lazy" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="/themes/custom/bootstrap4grow/images/wiedza.png" />
        </div>
        <p class="methodology-item-text mb-xl-0 d-xl-flex flex-row">
          <span class="h5 text-black d-xl-flex align-items-center fw-bold methodology-item-title">{{ "Wiedza" | trans }}</span>
          <span class="methodology-item-desc text-black d-xl-flex align-items-center">{{ "niezbędne informacje, teorie przełamujące schematy i mity, badania i odkrycia z różnych dziedzin: psychologii, neurobiologii, coachingu, NLP" | trans }}</span>
        </p>
      </div>
    </div>
  </div>
</div>
{% if label_hidden %}
  {% if multiple %}
    <div{{ attributes.addClass(classes, 'field__items') }}>
      {% for item in items %}
        <div{{ item.attributes.addClass('field__item') }}>{{ item.content }}</div>
      {% endfor %}
    </div>
  {% else %}
    {% for item in items %}
      <div{{ attributes.addClass(classes, 'field__item') }}>{{ item.content }}</div>
    {% endfor %}
  {% endif %}
{% else %}
  <div{{ attributes.addClass(classes) }}>
    <div{{ title_attributes.addClass(title_classes) }}>{{ label }}</div>
    {% if multiple %}
      <div class='field__items'>
    {% endif %}
    {% for item in items %}
      <div{{ item.attributes.addClass('field__item') }}>{{ item.content }}</div>
    {% endfor %}
    {% if multiple %}
      </div>
    {% endif %}
  </div>
{% endif %}
