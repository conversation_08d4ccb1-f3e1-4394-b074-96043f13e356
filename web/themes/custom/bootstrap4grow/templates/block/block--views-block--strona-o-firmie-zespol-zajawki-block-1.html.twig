{#
/**
 * @file
 * Default theme implementation to display a block.
 *
 * Available variables:
 * - plugin_id: The ID of the block implementation.
 * - label: The configured label of the block if visible.
 * - configuration: A list of the block's configuration values.
 *   - label: The configured label for the block.
 *   - label_display: The display settings for the label.
 *   - provider: The module or other provider that provided this block plugin.
 *   - Block plugin specific settings will also be stored here.
 * - content: The content of this block.
 * - attributes: array of HTML attributes populated by modules, intended to
 *   be added to the main container tag of this template.
 *   - id: A valid HTML ID and guaranteed unique.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - content_attributes: Same as attributes, except applied to the main content
 *   tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @see template_preprocess_block()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'block',
    'block-' ~ configuration.provider|clean_class,
    'block-' ~ plugin_id|clean_class,
  ]
%}
<div{{ attributes.addClass(classes) }} vocab="https://schema.org/" typeof="Organization">
      <meta property="name" content="4GROW"/>
      <meta property="legalName" content="Mateusz Dąbrowski 4GROW"/>
      <meta property="url" content="https://4grow.pl"/>
      <meta property="email" content="mailto:<EMAIL>"/>
      <meta property="telephone" content="531314431"/>
      <meta property="hoursAvailable" content="Mo-Fri 08:00-17:00"/>
      <meta property="description" content="4GROW to butikowa, rodzinna firma szkoleniowa oferująca szkolenia najwyższej jakości z obszaru kompetencji miękkich, osobistych i menedżerskich"/>
      <meta property="alternateName" content="4grow.pl"/>
      <meta property="mainEntityOfPage" content="https://4grow.pl"/>
      <meta property="sameAs" content="https://www.facebook.com/4GROW/"/>
      <img class="d-none" src="/sites/default/files/logo.png" width="328" height="87" property="logo" alt="4GROW"/>
      <div class="d-none" property="address" typeof="PostalAddress">
        <span property="streetAddress">Panieńska 9 lok. 25</span>
        <span property="postalCode">03-704</span>
        <span property="addressCountry">PL</span>
        <span property="addressLocality">Warszawa</span>
      </div>
      <picture class="d-none">
        <source srcset="/themes/custom/bootstrap4grow/images/O%20firmie.webp" type="image/webp">
        <img src="/themes/custom/bootstrap4grow/images/O%20firmie.jpg" height="192" width="420" property="image" alt="4GROW" />
      </picture>
      <meta property="seeks" content="firma szkoleniowa"/>
      <meta property="seeks" content="szkolenia z kompetencji miękkich"/>
      <meta property="seeks" content="szkolenia menadżerskie"/>
  {{ title_prefix }}
  {% if label %}
    <h2{{ title_attributes }}>{{ label }}</h2>
  {% endif %}
  {{ title_suffix }}
  {% block content %}
    {{ content }}
  {% endblock %}
</div>
