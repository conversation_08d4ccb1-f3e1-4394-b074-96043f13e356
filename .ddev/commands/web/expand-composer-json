#!/bin/bash

#ddev-generated
## Command provided by https://github.com/ddev/ddev-drupal-contrib
## Description: Add Drupal core and other needed dependencies.
## Usage: expand-composer-json [flags] [PROJECT_NAME]
## Example: "ddev expand-composer-json ctools"
## ExecRaw: true

export _WEB_ROOT=$DDEV_DOCROOT
cd "$DDEV_COMPOSER_ROOT" || exit
curl -OL https://git.drupalcode.org/project/gitlab_templates/-/raw/default-ref/scripts/expand_composer_json.php
php expand_composer_json.php "$DDEV_SITENAME"
rm -f expand_composer_json.php
