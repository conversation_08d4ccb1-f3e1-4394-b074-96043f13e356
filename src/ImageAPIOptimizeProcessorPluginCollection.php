<?php

namespace Drupal\imageapi_optimize;

use <PERSON>upal\Core\Plugin\DefaultLazyPluginCollection;

/**
 * A collection of image optimize processors.
 */
class ImageAPIOptimizeProcessorPluginCollection extends DefaultLazyPluginCollection {

  /**
   * {@inheritdoc}
   *
   * @return \Drupal\imageapi_optimize\ImageAPIOptimizeProcessorInterface
   */
  public function &get($instance_id) {
    return parent::get($instance_id);
  }

  /**
   * {@inheritdoc}
   */
  public function sortHelper($aID, $bID) {
    $a_weight = $this->get($aID)->getWeight();
    $b_weight = $this->get($bID)->getWeight();
    if ($a_weight == $b_weight) {
      return 0;
    }

    return ($a_weight < $b_weight) ? -1 : 1;
  }

}
