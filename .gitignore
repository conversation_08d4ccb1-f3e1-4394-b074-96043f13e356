# This file is managed by dropfort/dropfort_module_build.
# Delete this file and run `composer update dropfort/dropfort_module_build` to regenerate it.

# OS files.
.DS_Store
Thumbs.db

# Directories generated by NPM/Composer.
node_modules
vendor
bin

# Log files.
*.log*

# Autoload file.
autoload.php

# Drupal ignore directory.
# Used for ignoring possible core scaffolded files that are unwanted in a module.
drupal-ignore

# Ignore compiled code.
# Comment out if your build process will not handle compiling.
# dist

# Ignore lock files.
# Comment out if lock files are required.
composer.lock
package-lock.json
