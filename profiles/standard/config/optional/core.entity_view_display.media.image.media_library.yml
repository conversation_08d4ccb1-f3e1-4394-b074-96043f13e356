langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.media_library
    - field.field.media.image.field_media_image
    - image.style.medium
    - media.type.image
  module:
    - image
id: media.image.media_library
targetEntityType: media
bundle: image
mode: media_library
content:
  thumbnail:
    type: image
    label: hidden
    settings:
      image_style: medium
      image_link: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  created: true
  field_media_image: true
  name: true
  uid: true
