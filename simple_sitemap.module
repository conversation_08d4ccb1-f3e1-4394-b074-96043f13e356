<?php

/**
 * @file
 * Main module file containing hooks.
 */

use Dr<PERSON>al\Core\Entity\EntityInterface;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;
use Dr<PERSON>al\language\ConfigurableLanguageInterface;
use Dr<PERSON>al\simple_sitemap\Queue\QueueWorker;
use Dr<PERSON>al\system\MenuInterface;

/**
 * Implements hook_help().
 */
function simple_sitemap_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.simple_sitemap':
      return check_markup(file_get_contents(__DIR__ . '/README.md'));

    case 'simple_sitemap.entities':
      return '<p>' . t('Simple XML Sitemap settings will be added only to entity forms of entity types enabled here. Settings for specific entity bundles (e.g. <em>page</em>) can be adjusted here or on the bundle pages.') . '</p>';

    case 'simple_sitemap.custom':
      return '<p>' . t('Add custom internal drupal paths to specific sitemaps.') . '</p>'
        . '<p>' . t("Specify drupal internal (relative) paths, one per line. Do not forget to prepend the paths with a '/'.<br>Optionally link priority <em>(0.0 - 1.0)</em> can be added by appending it after a space.<br> Optionally link change frequency <em>(always / hourly / daily / weekly / monthly / yearly / never)</em> can be added by appending it after a space.") . '</p>'
        . '<p>' . t('<strong>Examples:</strong><br><em>/ 1.0 daily</em> -> home page with the highest priority and daily change frequency<br><em>/contact</em> -> contact page with the default priority and no change frequency information') . '</p>';

    default:
      return NULL;
  }
}

/**
 * Implements hook_form_alter().
 *
 * Adds sitemap settings to entity types that are supported via plugins.
 */
function simple_sitemap_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  /** @var \Drupal\simple_sitemap\Form\FormHelper $form_helper */
  $form_helper = \Drupal::service('simple_sitemap.form_helper');
  $form_helper->formAlter($form, $form_state);
}

/**
 * Implements hook_cron().
 */
function simple_sitemap_cron() {
  /** @var \Drupal\simple_sitemap\Settings $settings */
  $settings = \Drupal::service(('simple_sitemap.settings'));

  if ($settings->get('cron_generate')) {

    $interval = (int) $settings->get('cron_generate_interval', 0) * 60 * 60;
    $request_time = \Drupal::service('datetime.time')->getRequestTime();
    $state = \Drupal::state();

    /** @var \Drupal\simple_sitemap\Queue\QueueWorker $queue_worker */
    $queue_worker = \Drupal::service('simple_sitemap.queue_worker');
    $generation_in_progress = $queue_worker->generationInProgress();

    if ($interval === 0
      || $generation_in_progress
      || (($state->get('simple_sitemap.last_cron_generate', 0) + $interval) <= $request_time)) {

      if (!$generation_in_progress) {
        $state->set('simple_sitemap.last_cron_generate', $request_time);
      }

      /** @var \Drupal\simple_sitemap\Manager\Generator $generator */
      $generator = \Drupal::service('simple_sitemap.generator');
      $generator->generate(QueueWorker::GENERATE_TYPE_CRON);
    }
  }
}

/**
 * Implements hook_ENTITY_TYPE_delete().
 *
 * When a language is removed from the system remove it also from settings.
 */
function simple_sitemap_configurable_language_delete(ConfigurableLanguageInterface $language) {

  /** @var \Drupal\simple_sitemap\Settings $settings */
  $settings = \Drupal::service('simple_sitemap.settings');

  $excluded_languages = $settings->get('excluded_languages', []);
  if (isset($excluded_languages[$language->id()])) {
    unset($excluded_languages[$language->id()]);
    $settings->save('excluded_languages', $excluded_languages);
  }
}

/**
 * Implements hook_entity_delete().
 *
 * Removes settings of the removed entity.
 */
function simple_sitemap_entity_delete(EntityInterface $entity) {

  /** @var \Drupal\simple_sitemap\Entity\EntityHelper $entity_helper */
  $entity_helper = \Drupal::service('simple_sitemap.entity_helper');
  if ($entity_helper->supports($entity->getEntityType())) {

    /** @var \Drupal\simple_sitemap\Manager\Generator $generator */
    $generator = \Drupal::service('simple_sitemap.generator');
    $generator->entityManager()->setSitemaps()->removeEntityInstanceSettings(
      $entity->getEntityTypeId(), $entity->id()
    );
  }
}

/**
 * Implements hook_entity_bundle_delete().
 *
 * Removes settings of the removed bundle.
 *
 * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
 * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
 */
function simple_sitemap_entity_bundle_delete($entity_type_id, $bundle) {

  /** @var \Drupal\simple_sitemap\Manager\Generator $generator */
  $generator = \Drupal::service('simple_sitemap.generator');
  $generator->entityManager()->setSitemaps()->removeBundleSettings($entity_type_id, $bundle);
}

/**
 * Implements hook_menu_delete().
 *
 * Removes settings for the removed menu.
 *
 * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
 * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
 */
function simple_sitemap_menu_delete(MenuInterface $menu) {

  /** @var \Drupal\simple_sitemap\Manager\Generator $generator */
  $generator = \Drupal::service('simple_sitemap.generator');
  $generator->entityManager()->setSitemaps()->removeBundleSettings('menu_link_content', $menu->id());
}

/**
 * Implements hook_page_attachments_alter().
 */
function simple_sitemap_page_attachments_alter(array &$attachments) {
  if (!empty($attachments['#attached']['html_head_link'])) {

    /** @var \Drupal\simple_sitemap\Settings $settings */
    $settings = \Drupal::service('simple_sitemap.settings');

    if ($settings->get('disable_language_hreflang')) {
      // @fixme https://www.drupal.org/project/drupal/issues/1255092
      // Content Translation module normally adds identical hreflang tags, so
      // executing its hook_page_attachments() implementation would be harmless,
      // but if an entity page is configured as the front page, it attaches
      // extraneous hreflang tags using the entity URL.
      foreach ($attachments['#attached']['html_head_link'] as $key => $list) {
        foreach ($list as $element) {
          if (!empty($element['hreflang']) && !empty($element['rel'])) {
            unset($attachments['#attached']['html_head_link'][$key]);
          }
        }
      }
    }
  }
}

/**
 * Implements hook_entity_extra_field_info().
 */
function simple_sitemap_entity_extra_field_info() {
  $extra = [];

  /** @var \Drupal\simple_sitemap\Entity\EntityHelper $entity_helper */
  $entity_helper = \Drupal::service('simple_sitemap.entity_helper');

  /** @var \Drupal\simple_sitemap\Manager\EntityManager $entity_manager */
  $entity_manager = \Drupal::service('simple_sitemap.entity_manager');

  foreach ($entity_helper->getSupportedEntityTypes() as $entity_type_id => $entity_type) {
    if ($entity_type->get('field_ui_base_route') && $entity_manager->entityTypeIsEnabled($entity_type_id)) {

      foreach ($entity_helper->getBundleInfo($entity_type_id) as $bundle_name => $bundle_info) {
        $extra[$entity_type_id][$bundle_name]['form']['simple_sitemap'] = [
          'label' => t('Simple XML Sitemap'),
          'description' => t('Simple XML Sitemap settings'),
          'weight' => 10,
        ];
      }
    }
  }
  return $extra;
}
