<?php

/**
 * @file
 * Main file for the scn module.
 */

use Drupal\Core\Entity\EntityInterface;
use <PERSON>upal\user\Entity\User;
use Drupal\Core\Routing\RouteMatchInterface;
use Drupal\Core\Url;

/**
 * Implements hook_help().
 */
function scn_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.scn':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Simple Comment Notify module notifies the site administrator by e-mail or telegram about new comments on the site.') . '</p>';
      return $output;

    default:
  }
}

/**
 * Implements hook_mail().
 */
function scn_mail($key, &$message, $params) {
  switch ($key) {
    case 'new_comment':
      $message['from'] = \Drupal::config('system.site')->get('mail');
      $message['subject'] = t('New comment on @siteName', [
        '@siteName' => \Drupal::config('system.site')->get('name'),
      ]);
      $message['body'][] = t('Link to the new comment :linkToComment', [
        ':linkToComment' => $params['url'],
      ]);
      break;
  }
}

/**
 * Send mail common function.
 */
function _scn_send_mail($to, $subject, $params) {
  if (\Drupal::service('email.validator')->isValid($to)) {
    $mailManager = \Drupal::service('plugin.manager.mail');
    $langcode = \Drupal::currentUser()->getPreferredLangcode();

    $mailManager->mail('scn', 'new_comment', $to, $langcode, $params, NULL, TRUE);

    \Drupal::logger('Simple Comment Notify')->notice(t('Sent eMail notification for comment with subject "@subject"
    to the site administrator eMail address :siteAdministratorEmailAddress', [
      '@subject'                       => $subject,
      ':siteAdministratorEmailAddress' => $to,
    ]));
  }
}

/**
 * Send to telegram common function.
 */
function _scn_send_telegram($url, $token, $chatid, $proxy = FALSE, $proxy_server = NULL, $proxy_login = NULL, $proxy_pass = NULL) {
  $curl = curl_init();

  if ($curl) {
    $text = t('New comment on @siteName: @url', [
      '@siteName' => \Drupal::config('system.site')->get('name'),
      '@url' => $url,
    ]);

    $query = "https://api.telegram.org/bot" . $token .
      "/sendMessage?disable_web_page_preview=true&chat_id=" . $chatid .
      "&text=" . urlencode($text);

    curl_setopt($curl, CURLOPT_URL, $query);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($curl, CURLOPT_POST, TRUE);
    if ($proxy) {
      curl_setopt($curl, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
      curl_setopt($curl, CURLOPT_PROXY, $proxy_server);
      if ($proxy_login) {
        curl_setopt($curl, CURLOPT_PROXYUSERPWD, $proxy_login . ':' . $proxy_pass);
      }
    }

    if (curl_exec($curl) === FALSE) {
      \Drupal::logger('Simple Comment Notify')->notice(curl_error($curl));
    }

    curl_close($curl);
  }
  else {
    \Drupal::logger('Simple Comment Notify')->notice(t('Cannot initialize cURL. Is it installed on the server?'));
  }

}

/**
 * Implements hook_entity_insert().
 */
function scn_entity_insert(EntityInterface $entity) {
  if ($entity->getEntityTypeId() == 'comment') {
    $config = \Drupal::config('scn.settings');
    $subject = $entity->getSubject();
    $url = $entity->permalink()->setOption('absolute', TRUE)->toString();
    $site_url = \Drupal::request()->getSchemeAndHttpHost();

    if ($config->get('scn_add_admin_overview_link')) {
      $url .= "\r\n" . t('Comments overview page:') . ' ' . Url::fromUri($site_url . '/admin/content/comment/approval')->toString();
    }

    if ($config->get('scn_add_admin_comment_link')) {
      $url .= "\r\n" . t('Comment admin page:') . ' ' . Url::fromUri($site_url . '/comment/' . $entity->id() . '/edit?destination=/admin/content/comment/approval')->toString();
    }

    $params = [
      'url' => $url,
      'comment' => $entity,
    ];

    // Send to admin.
    if ($config->get('scn_admin')) {
      $account = User::load(1);
      $to = $account->getEmail();
      if ($to) {
        _scn_send_mail($to, $subject, $params);
      }
    }

    // Send to users with roles.
    if ($config->get('scn_roles')) {
      foreach (array_values($config->get('scn_roles')) as $user_role) {
        if ($user_role !== 0) {
          $ids = \Drupal::entityQuery('user')
            ->condition('status', 1)
            ->condition('roles', $user_role)
            ->accessCheck(false)
            ->execute();
          $users = User::loadMultiple($ids);
          if (!empty(array_filter($users))) {
            foreach ($users as $user) {
              $to = $user->getEmail();
              if ($to) {
                _scn_send_mail($to, $subject, $params);
              }
            }
          }
        }
      }
    }

    // Send to non-registered users.
    if (!empty($config->get('scn_maillist'))) {
      $mails = explode(',', $config->get('scn_maillist'));
      foreach ($mails as $mail) {
        $to = $mail;
        if ($to) {
          _scn_send_mail($to, $subject, $params);
        }
      }
    }

    // Send to node author.
    if ($config->get('scn_node_author')) {
      $node = $entity->getCommentedEntity();
      $node_author = $node->getOwner();
      $to = $node_author->getEmail();
      if ($to) {
        _scn_send_mail($to, $subject, $params);
      }
    }

    // Send message to telegram.
    if ($config->get('scn_telegram')) {
      $token = $config->get('scn_telegram_bottoken');
      $chatids = explode(',', $config->get('scn_telegram_chatids'));
      foreach ($chatids as $chatid) {
        if ($config->get('scn_telegram_proxy')) {
          $proxy_server = $config->get('scn_telegram_proxy_server');
          $proxy_login = $config->get('scn_telegram_proxy_login');
          $proxy_password = $config->get('scn_telegram_proxy_password');
          _scn_send_telegram($url, $token, $chatid, TRUE, $proxy_server, $proxy_login, $proxy_password);
        }
        else {
          _scn_send_telegram($url, $token, $chatid, $subject);
        }
      }
    }
  }
}
