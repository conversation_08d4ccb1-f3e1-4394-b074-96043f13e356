<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'mie', 'xu', 'mang', 'chi', 'ge', 'xuan', 'yao', 'zi', 'he', 'ji', 'diao', 'cun', 'tong', 'ming', 'hou', 'li',
  0x10 => 'tu', 'xiang', 'zha', 'xia', 'ye', 'lu', 'ya', 'ma', 'ou', 'huo', 'yi', 'jun', 'chou', 'lin', 'tun', 'yin',
  0x20 => 'fei', 'bi', 'qin', 'qin', 'jie', 'bu', 'fou', 'ba', 'dun', 'fen', 'e', 'han', 'ting', 'keng', 'shun', 'qi',
  0x30 => 'hong', 'zhi', 'yin', 'wu', 'wu', 'chao', 'ne', 'xue', 'xi', 'chui', 'dou', 'wen', 'hou', 'hong', 'wu', 'gao',
  0x40 => 'ya', 'jun', 'lu', 'e', 'ge', 'mei', 'dai', 'qi', 'cheng', 'wu', 'gao', 'fu', 'jiao', 'hong', 'chi', 'sheng',
  0x50 => 'ne', 'tun', 'fu', 'yi', 'dai', 'ou', 'li', 'bei', 'yuan', 'guo', 'wen', 'qiang', 'wu', 'e', 'shi', 'juan',
  0x60 => 'pen', 'wen', 'ne', 'm', 'ling', 'ran', 'you', 'di', 'zhou', 'shi', 'zhou', 'tie', 'xi', 'yi', 'qi', 'ping',
  0x70 => 'zi', 'gu', 'ci', 'wei', 'xu', 'a', 'nao', 'ga', 'pei', 'yi', 'xiao', 'shen', 'hu', 'ming', 'da', 'qu',
  0x80 => 'ju', 'han', 'za', 'tuo', 'duo', 'pou', 'pao', 'bie', 'fu', 'yang', 'he', 'za', 'he', 'hai', 'jiu', 'yong',
  0x90 => 'fu', 'da', 'zhou', 'wa', 'ka', 'gu', 'ka', 'zuo', 'bu', 'long', 'dong', 'ning', 'ta', 'si', 'xian', 'huo',
  0xA0 => 'qi', 'er', 'e', 'guang', 'zha', 'xi', 'yi', 'lie', 'zi', 'mie', 'mi', 'zhi', 'yao', 'ji', 'zhou', 'ge',
  0xB0 => 'shu', 'zan', 'xiao', 'hai', 'hui', 'kua', 'huai', 'tao', 'xian', 'e', 'xuan', 'xiu', 'guo', 'yan', 'lao', 'yi',
  0xC0 => 'ai', 'pin', 'shen', 'tong', 'hong', 'xiong', 'duo', 'wa', 'ha', 'zai', 'you', 'die', 'pai', 'xiang', 'ai', 'gen',
  0xD0 => 'kuang', 'ya', 'da', 'xiao', 'bi', 'hui', 'nian', 'hua', 'xing', 'kuai', 'duo', 'fen', 'ji', 'nong', 'mou', 'yo',
  0xE0 => 'hao', 'yuan', 'long', 'pou', 'mang', 'ge', 'o', 'chi', 'shao', 'li', 'na', 'zu', 'he', 'ku', 'xiao', 'xian',
  0xF0 => 'lao', 'bo', 'zhe', 'zha', 'liang', 'ba', 'mie', 'lie', 'sui', 'fu', 'bu', 'han', 'heng', 'geng', 'shuo', 'ge',
];
