<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'luk', 'lut', 'lup', 'luh', 'lwo', 'lwog', 'lwokk', 'lwogs', 'lwon', 'lwonj', 'lwonh', 'lwod', 'lwol', 'lwolg', 'lwolm', 'lwolb',
  0x10 => 'lwols', 'lwolt', 'lwolp', 'lwolh', 'lwom', 'lwob', 'lwobs', 'lwos', 'lwoss', 'lwong', 'lwoj', 'lwoch', 'lwok', 'lwot', 'lwop', 'lwoh',
  0x20 => 'lwe', 'lweg', 'lwekk', 'lwegs', 'lwen', 'lwenj', 'lwenh', 'lwed', 'lwel', 'lwelg', 'lwelm', 'lwelb', 'lwels', 'lwelt', 'lwelp', 'lwelh',
  0x30 => 'lwem', 'lweb', 'lwebs', 'lwes', 'lwess', 'lweng', 'lwej', 'lwech', 'lwek', 'lwet', 'lwep', 'lweh', 'lwi', 'lwig', 'lwikk', 'lwigs',
  0x40 => 'lwin', 'lwinj', 'lwinh', 'lwid', 'lwil', 'lwilg', 'lwilm', 'lwilb', 'lwils', 'lwilt', 'lwilp', 'lwilh', 'lwim', 'lwib', 'lwibs', 'lwis',
  0x50 => 'lwiss', 'lwing', 'lwij', 'lwich', 'lwik', 'lwit', 'lwip', 'lwih', 'lyu', 'lyug', 'lyukk', 'lyugs', 'lyun', 'lyunj', 'lyunh', 'lyud',
  0x60 => 'lyul', 'lyulg', 'lyulm', 'lyulb', 'lyuls', 'lyult', 'lyulp', 'lyulh', 'lyum', 'lyub', 'lyubs', 'lyus', 'lyuss', 'lyung', 'lyuj', 'lyuch',
  0x70 => 'lyuk', 'lyut', 'lyup', 'lyuh', 'leu', 'leug', 'leukk', 'leugs', 'leun', 'leunj', 'leunh', 'leud', 'leul', 'leulg', 'leulm', 'leulb',
  0x80 => 'leuls', 'leult', 'leulp', 'leulh', 'leum', 'leub', 'leubs', 'leus', 'leuss', 'leung', 'leuj', 'leuch', 'leuk', 'leut', 'leup', 'leuh',
  0x90 => 'lui', 'luig', 'luikk', 'luigs', 'luin', 'luinj', 'luinh', 'luid', 'luil', 'luilg', 'luilm', 'luilb', 'luils', 'luilt', 'luilp', 'luilh',
  0xA0 => 'luim', 'luib', 'luibs', 'luis', 'luiss', 'luing', 'luij', 'luich', 'luik', 'luit', 'luip', 'luih', 'li', 'lig', 'likk', 'ligs',
  0xB0 => 'lin', 'linj', 'linh', 'lid', 'lil', 'lilg', 'lilm', 'lilb', 'lils', 'lilt', 'lilp', 'lilh', 'lim', 'lib', 'libs', 'lis',
  0xC0 => 'liss', 'ling', 'lij', 'lich', 'lik', 'lit', 'lip', 'lih', 'ma', 'mag', 'makk', 'mags', 'man', 'manj', 'manh', 'mad',
  0xD0 => 'mal', 'malg', 'malm', 'malb', 'mals', 'malt', 'malp', 'malh', 'mam', 'mab', 'mabs', 'mas', 'mass', 'mang', 'maj', 'mach',
  0xE0 => 'mak', 'mat', 'map', 'mah', 'mae', 'maeg', 'maekk', 'maegs', 'maen', 'maenj', 'maenh', 'maed', 'mael', 'maelg', 'maelm', 'maelb',
  0xF0 => 'maels', 'maelt', 'maelp', 'maelh', 'maem', 'maeb', 'maebs', 'maes', 'maess', 'maeng', 'maej', 'maech', 'maek', 'maet', 'maep', 'maeh',
];
