<?php

namespace Drupal\Core\TypedData\Plugin\DataType;

use <PERSON>upal\Core\StringTranslation\TranslatableMarkup;
use <PERSON>upal\Core\TypedData\Attribute\DataType;
use <PERSON>upal\Core\TypedData\PrimitiveBase;
use <PERSON>upal\Core\TypedData\Type\StringInterface;

/**
 * The string data type.
 *
 * The plain value of a string is a regular PHP string. For setting the value
 * any PHP variable that casts to a string may be passed.
 */
#[DataType(
  id: "string",
  label: new TranslatableMarkup("String")
)]
class StringData extends PrimitiveBase implements StringInterface {

  /**
   * {@inheritdoc}
   */
  public function getCastedValue() {
    return $this->getString();
  }

}
