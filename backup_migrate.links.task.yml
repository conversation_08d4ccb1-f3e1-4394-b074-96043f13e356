backup_migrate.quick_backup:
  title: Backup
  route_name: backup_migrate.quick_backup
  base_route: backup_migrate.quick_backup

backup_migrate.restore:
  title: Restore
  route_name: backup_migrate.restore
  base_route: backup_migrate.quick_backup

backup_migrate.backups:
  title: Saved Backups
  route_name: backup_migrate.backups
  base_route: backup_migrate.quick_backup

backup_migrate.quick_backup_sub:
  title: Quick Backup
  route_name: backup_migrate.quick_backup
  parent_id: backup_migrate.quick_backup

backup_migrate.advanced_backup:
  title: Advanced Backup
  route_name: backup_migrate.advanced_backup
  parent_id: backup_migrate.quick_backup

backup_migrate.schedule:
  title: Schedules
  route_name: entity.backup_migrate_schedule.collection
  base_route: backup_migrate.quick_backup

backup_migrate.settings:
  title: Settings
  route_name: entity.backup_migrate_settings.collection
  base_route: backup_migrate.quick_backup

backup_migrate.settings_profiles:
  title: Settings Profiles
  route_name: entity.backup_migrate_settings.collection
  base_route: backup_migrate.quick_backup
  parent_id: backup_migrate.settings


backup_migrate.destination:
  title: Destinations
  route_name: entity.backup_migrate_destination.collection
  base_route: backup_migrate.quick_backup
  parent_id: backup_migrate.settings


backup_migrate.source:
  title: Sources
  route_name: entity.backup_migrate_source.collection
  base_route: backup_migrate.quick_backup
  parent_id: backup_migrate.settings
