{#
/**
 * @file
 * <PERSON><PERSON><PERSON>'s theme implementation to display a single Drupal page.
 *
 * The doctype, html, head, and body tags are not in this template. Instead
 * they can be found in the html.html.twig template normally located in the
 * core/modules/system directory.
 *
 * Available variables:
 *
 * General utility variables:
 * - base_path: The base URL path of the Drupal installation. Will usually be
 *   "/" unless you have installed <PERSON><PERSON><PERSON> in a sub-directory.
 * - is_front: A flag indicating if the current page is the front page.
 * - logged_in: A flag indicating if the user is registered and signed in.
 * - is_admin: A flag indicating if the user has permission to access
 *   administration pages.
 *
 * Site identity:
 * - front_page: The URL of the front page. Use this instead of base_path when
 *   linking to the front page. This includes the language domain or prefix.
 *
 * Page content (in order of occurrence in the default page.html.twig):
 * - node: Fully loaded node, if there is an automatically-loaded node
 *   associated with the page and the node ID is the second argument in the
 *   page's path (e.g. node/12345 and node/12345/revisions, but not
 *   comment/reply/12345).
 *
 * Regions:
 * - page.header: Items for the header region.
 * - page.pre_content: Items for the pre-content region.
 * - page.breadcrumb: Items for the breadcrumb region.
 * - page.highlighted: Items for the highlighted region.
 * - page.help: Dynamic help text, mostly for admin pages.
 * - page.content: The main content of the current page.
 *
 * @see template_preprocess_page()
 * @see html.html.twig
 */
#}
  <main>
    <div class="visually-hidden"><a id="main-content" tabindex="-1"></a></div>
    {% if page.breadcrumb or page.header %}
      <header class="content-header clearfix">
        <div class="layout-container">
          {{ page.breadcrumb }}
          {{ page.header }}
        </div>
      </header>
    {% endif %}

    <div class="layout-container">
      {{ page.pre_content }}
      <div class="page-content clearfix">
        {{ page.highlighted }}
        {% if page.help %}
          <div class="help">
            {{ page.help }}
          </div>
        {% endif %}
        {{ page.content }}
      </div>

    </div>
  </main>
