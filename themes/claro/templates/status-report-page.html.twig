{#
/**
 * @file
 * Theme override for the status report page.
 *
 * Available variables:
 * - counters: The list of counter elements.
 * - general_info: A render array to create general info element.
 * - requirements: A render array to create requirements table.
 *
 * @see template_preprocess_status_report()
 */
#}
{% if counters|length == 3 %}
  {% set element_width_class = ' system-status-report-counters__item--third-width' %}
{% elseif counters|length == 2 %}
  {% set element_width_class = ' system-status-report-counters__item--half-width' %}
{% endif %}
<div class="system-status-report-counters">
  {% for counter in counters %}
    <div class="card system-status-report-counters__item{{ element_width_class }}">
      {{ counter }}
    </div>
  {% endfor %}
</div>

{{ general_info }}
{{ requirements }}
