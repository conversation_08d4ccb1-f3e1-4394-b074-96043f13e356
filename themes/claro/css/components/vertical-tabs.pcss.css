/**
 * @file
 * Vertical tabs module.
 *
 * Replaces /core/misc/vertical-tabs.css.
 */

/**
 * Main wrapper of vertical tabs.
 * This wrapper div is added by JavaScript.
 */
.vertical-tabs {
  margin-block: var(--vertical-tabs-margin-vertical);
  border-block-start: 1px solid transparent; /* Need to hide the pane wrapper clearfix's height */
}

/**
 * Vertical tabs menu.
 */
.vertical-tabs__menu {
  position: relative;
  display: block;
  float: left; /* LTR */
  width: var(--vertical-tabs-menu-width);
  margin: 0;
  padding-block-start: var(--vertical-tabs-menu-item-shadow-extraspace);
  list-style: none;
  color: var(--color-text);

  @nest [dir="rtl"] & {
    float: right;
  }
}

/**
 * Vertical tabs menu item.
 */
.vertical-tabs__menu-item {
  overflow: hidden;
  margin-block: var(--vertical-tabs-menu-item--top-margin);
  margin-inline: var(--vertical-tabs-menu-item--left-margin) var(--vertical-tabs-menu-item--right-margin);
  padding-block: var(--vertical-tabs-menu-item-shadow-extraspace);
  padding-inline: var(--vertical-tabs-menu-item-shadow-extraspace) 0;
}

/**
 * These are the (gray) lines that are visually separating the vertical tab menu
 * items.
 */
.vertical-tabs__menu-item::before,
.vertical-tabs__menu-item::after {
  position: absolute;
  z-index: 1; /* The line should be kept above the vertical tabs menu link to keep it visible even if the link is hovered and gets the 'hover' background color. */
  display: block;
  width: 100%;
  margin-block-start: calc(var(--vertical-tabs-menu-separator-size) * -1);
  border-block-start: var(--vertical-tabs-menu-separator-size) solid var(--vertical-tabs-menu-separator-color);
}

/**
 * This pseudo element covers the gray separator line of the vertical tab menu
 * item that follows the active one.
 *
 * Without this, we would have a lightgray line at the bottom-left (LTR) corner
 * of the active vertical tab menu item.
 *
 * Let's call this 'masking' line.
 */
.vertical-tabs__menu-item::after {
  z-index: 2;
  border-color: var(--color-white);
}

/**
 * Making the separator line visible only if it follows an unhidden menu item.
 */
.vertical-tabs__menu-item:not(.vertical-tab--hidden) ~ .vertical-tabs__menu-item::before {
  content: "";
}

/* Menu item states. */
.vertical-tabs__menu-item:focus {
  outline: 0;
  box-shadow: none;
}

.vertical-tabs__menu-item.is-selected::before,
.vertical-tabs__menu-item:not(.vertical-tab--hidden) ~ .vertical-tabs__menu-item.is-selected::before {
  content: normal;
}

/* Make the white masking line displayed for the selected menu item. */
.vertical-tabs__menu-item.is-selected::after {
  content: "";
}

/**
 * Anchor link inside the vertical tabs menu item.
 */
.vertical-tabs__menu-link {
  position: relative;
  display: block;
  margin-block-start: calc(var(--vertical-tabs-border-size) * -1);
  padding: var(--space-s) var(--space-s) var(--space-s) calc(var(--space-l) - var(--vertical-tabs-menu-link--active-border-size));
  text-decoration: none;
  color: var(--color-text);
  border: var(--vertical-tabs-border-size) solid transparent;
  border-block-width: var(--vertical-tabs-border-size);
  border-inline-width: var(--vertical-tabs-menu-link--active-border-size) 0;
  border-start-start-radius: var(--vertical-tabs-border-radius);
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-end-start-radius: var(--vertical-tabs-border-radius);

  &:focus {
    z-index: 4; /* Focus state should be on the highest level to make the focus effect be fully visible. This also means that it should have bigger z-index than the selected link. */
    text-decoration: none;
    box-shadow: none;

    &::after {
      position: absolute;
      inset: 0;
      margin: calc(var(--vertical-tabs-border-size) * -1) calc(var(--vertical-tabs-menu-link--active-border-size) * -1);
      content: "";
      pointer-events: none;
      border: var(--vertical-tabs-menu-link-focus-border-size) solid var(--color-focus);
      border-radius: var(--vertical-tabs-border-radius);
    }
  }

  &:hover {
    text-decoration: none;
    color: var(--color-absolutezero);
    &::before {
      background: var(--color-bgblue-hover);
    }
  }

  &::before {
    position: absolute;
    z-index: 0; /* This should be on a lower level than the menu-item separator lines. */
    inset-block: calc(var(--vertical-tabs-border-size) * -1);
    inset-inline: calc(var(--vertical-tabs-menu-link--active-border-size) * -1) 0;
    content: "";
    pointer-events: none;
    background-clip: padding-box;
  }
}

.vertical-tabs__menu-item.is-selected .vertical-tabs__menu-link {
  z-index: 3; /* The selected menu link should be on a higher level than the white masking line that hides the gray separator. */
  color: var(--color-absolutezero);
  border-color: var(--vertical-tabs-border-color) transparent;
  background-color: var(--color-white);
  box-shadow: var(--vertical-tabs-shadow);
}

.vertical-tabs__menu-item.is-selected .vertical-tabs__menu-link:hover {
  color: var(--color-absolutezero-hover);
  background-color: var(--color-bgblue-hover);
}

.vertical-tabs__menu-item.is-selected .vertical-tabs__menu-link::before {
  z-index: 1; /* The blue active-tab indication should be on a higher level than the green focus border. */
  border-inline-start: var(--vertical-tabs-menu-link--active-border-size) solid var(--vertical-tabs-menu-link--active-border-color);
  border-radius: var(--base-border-radius) 0 0 var(--base-border-radius);
  border-start-start-radius: var(--base-border-radius);
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-end-start-radius: var(--base-border-radius);

  @media (forced-colors: active) {
    border-inline-start: var(--vertical-tabs-menu-link--active-border-size) solid canvastext;
  }
}

.vertical-tabs__menu-item.is-selected .vertical-tabs__menu-link:hover::before {
  background: none;
}

.vertical-tabs__menu-link-content {
  position: relative;
  z-index: 1; /* We are using a pseudo element for displaying the hover state's background, and we have to keep the link content above that pseudo element. Without this, the text would be covered by the background. */
}

/**
 * Details summary in vertical tabs menu link and in the summary of the details.
 */
.vertical-tabs__menu-link-summary {
  display: block;
  color: var(--color-gray-800);
  font-size: var(--font-size-s);
  font-weight: normal;
}

/**
 * Wrapper of vertical tabs panes.
 */
.vertical-tabs__items {
  box-sizing: border-box;
  margin-block: var(--vertical-tabs-margin-vertical);
  color: var(--color-text);
  border: var(--vertical-tabs-border);
  border-radius: var(--vertical-tabs-border-radius);
  background-color: var(--color-white);
  box-shadow: var(--vertical-tabs-shadow);
}

/* This modifier is added by JavaScript (this is inherited from Drupal core). */
.vertical-tabs__panes {
  position: relative;
  z-index: 1; /* The wrapper of the details of the vertical tabs should be on a higher level than the vertical tabs menu */
  inset-block-start: -1px;
  margin-block: 0;

  &::after {
    display: block; /* This clearfix makes the pane wrapper at least as tall as the menu. */
    clear: both;
    content: "";
  }
}

.vertical-tabs .vertical-tabs__panes {
  margin-inline-start: var(--vertical-tabs-menu-width);
  border-top-left-radius: 0;

  @nest [dir="rtl"] & {
    border-top-left-radius: var(--vertical-tabs-border-radius);
  }
}

/**
 * The actual vertical tabs pane.
 *
 * This is a claro-details element which in this case is also
 * vertical-tabs__item.
 */
.vertical-tabs__item {
  /* Render on top of the border of vertical-tabs__items. */
  margin: calc(var(--vertical-tabs-border-size) * -1) calc(var(--vertical-tabs-border-size) * -1) 0;
  border-radius: 0;

  &.first {
    border-top-left-radius: var(--details-accordion-border-size-radius);
    border-top-right-radius: var(--details-accordion-border-size-radius);
  }

  &.last {
    margin-bottom: calc(var(--vertical-tabs-border-size) * -1);
    border-bottom-right-radius: var(--details-accordion-border-size-radius);
    border-bottom-left-radius: var(--details-accordion-border-size-radius);
  }
}

.js .vertical-tabs .vertical-tabs__item {
  overflow: hidden;
  margin: 0;
  border: 0;

  &.first,
  &.last {
    border-radius: 0;
  }

  & > summary {
    display: none;
  }
}
