/**
 * Generic elements.
 */

html {
  font-family: var(--font-family);
  font-size: 100%;
  font-weight: normal;
  font-style: normal;
  line-height: var(--line-height);
}
body {
  word-wrap: break-word;
  hyphens: auto;
  color: var(--color-fg);
  background: var(--color-bg);
}

a,
.link {
  color: var(--color-link);
}
a:hover,
a:focus,
.link:hover,
.link:focus {
  text-decoration: none;
  outline: 0;
}
a:hover,
.link:hover {
  color: var(--color-link-hover);
}
a:active,
.link:active {
  color: var(--color-link-active);
}
hr {
  height: 1px;
  margin: var(--space-m) 0;
  padding: 0;
  border: none;
  background: var(--color-divider);
}

summary {
  font-weight: bold;
}

/**
 * Reusable heading classes are included to help modules change the styling of
 * headings on a page without affecting accessibility.
 */
h1,
.heading-a {
  margin: var(--space-m) 0 var(--space-s);
  font-size: var(--font-size-h1);
  font-weight: bold;
  line-height: var(--line-height-heading);
}
h2,
.heading-b {
  margin: var(--space-m) 0 var(--space-s);
  font-size: var(--font-size-h2);
  font-weight: bold;
  line-height: var(--line-height-heading);
}
h3,
.heading-c {
  margin: var(--space-m) 0 var(--space-s);
  font-size: var(--font-size-h3);
  font-weight: bold;
  line-height: var(--line-height-heading);
}
h4,
.heading-d {
  margin: var(--space-m) 0 var(--space-s);
  font-size: var(--font-size-h4);
  font-weight: bold;
  line-height: var(--line-height-heading);
}
h5,
.heading-e {
  margin: var(--space-m) 0 var(--space-s);
  font-size: var(--font-size-h5);
  font-weight: bold;
  line-height: var(--line-height-heading);
}
h6,
.heading-f {
  margin: var(--space-m) 0 var(--space-s);
  font-size: var(--font-size-h6);
  font-weight: bold;
  line-height: var(--line-height-heading);
}
p {
  margin: 1em 0;
}
dl {
  margin: 0 0 20px;
}
dl dd,
dl dl {
  margin-block-end: 10px;
  margin-inline-start: 20px;
}
blockquote {
  position: relative;
  margin-block: var(--space-l);
  margin-inline: 2.5rem var(--space-l);
  font-size: var(--font-size-h6);
}
blockquote::before {
  position: absolute;
  inset-inline-start: -2.5rem;
  content: open-quote;
  color: var(--color-absolutezero);
  font-family: var(--font-family-serif);
  font-size: var(--space-xl);
  line-height: 1em;
}
blockquote::after {
  content: no-close-quote;
}
address {
  font-style: italic;
}
u,
ins {
  text-decoration: underline;
}
s,
strike,
del {
  text-decoration: line-through;
}
big {
  font-size: larger;
}
small {
  font-size: smaller;
}
sub {
  vertical-align: sub;
  font-size: smaller;
  line-height: normal;
}
sup {
  vertical-align: super;
  font-size: smaller;
  line-height: normal;
}
abbr,
acronym {
  border-bottom: dotted 1px;
}
ul {
  margin-block: 0.25em;
  margin-inline: 1.5em 0;
  padding-inline-start: 0;
  list-style-type: disc;
  list-style-image: none;
}
ol {
  margin-block: 0.25em;
  margin-inline: 2em 0;
  padding: 0;
}

code {
  margin: 0.5em 0;
}
pre {
  margin: 0.5em 0;
  white-space: pre-wrap;
}
details {
  line-height: var(--details-line-height);

  & summary {
    padding: 0.95em 1.45em;

    &:focus {
      outline: none;
    }
  }
}
img {
  max-width: 100%;
  height: auto;
}

/**
 * Default focus styles for focused elements.
 *
 * This is applied globally to all interactive elements except Toolbar and
 * Settings Tray since they have their own styles.
 */
.page-wrapper *:focus,
.ui-dialog *:focus {
  outline: var(--focus-outline);
  box-shadow: var(--focus-box-shadow);
}
