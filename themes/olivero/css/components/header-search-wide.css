/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Header Search Wide Block.
 */

/* Override contextual links so we can position against .site-header. */

.block-search-wide.contextual-region {
  position: static;
}

.block-search-wide__wrapper {
  position: absolute;
  z-index: 1; /* Ensure left border shows above social region in IE11. */
  inset-block-start: 100%;
  inset-inline-start: calc(-1 * var(--content-left));
  display: none;
  visibility: hidden;
  overflow: hidden;
  width: calc(100% + var(--content-left));
  max-width: var(--max-bg-color);
  height: var(--sp8);
  max-height: 0;
  margin-block: 0;
  margin-inline-start: 0;
  margin-inline-end: 0;
  padding-block: 0;
  padding-inline-start: 0;
  padding-inline-end: 0;
  transition: all 0.2s;
  border-inline-start: solid var(--content-left) var(--color--gray-20);
  background: var(--color--black);
}

.block-search-wide__wrapper.is-active {
  visibility: visible;
  max-height: var(--sp8);
}

.block-search-wide__wrapper form {
  display: flex;
  grid-column: 1 / 14;
}

.block-search-wide__wrapper input[type="search"] {
  width: calc(100% + var(--sp2));
  height: var(--sp8);
  padding-block: 0;
  padding-inline-start: var(--sp12);
  padding-inline-end: 0;
  transition: background-size 0.4s;
  color: var(--color--white);
  border: solid 1px transparent;
  box-shadow: none;
  font-family: var(--font-serif);
  font-size: 2rem;
  -webkit-appearance: none;
}

.block-search-wide__wrapper input[type="search"]:focus {
  outline: solid 4px transparent;
  outline-offset: -4px;
}

.block-search-wide__wrapper .form-item-keys {
  flex-grow: 1;
  margin: 0;
}

.block-search-wide__wrapper .form-actions {
  display: flex;
  margin: 0;
}

.block-search-wide__wrapper .search-form__submit {
  position: relative;
  overflow: hidden;
  align-self: stretch;
  width: 6.25rem;
  height: auto;
  margin-block: 0;
  margin-inline-start: 0;
  margin-inline-end: 0;
  padding-block: 0;
  padding-inline-start: 0;
  padding-inline-end: 0;
  cursor: pointer;
  border-color: transparent;
  background-color: transparent;

  /*
      When in Windows high contrast mode, FF will not output either background
      images or SVGs that are nested directly within a <button> element, so we add a <span>.
    */
}

.block-search-wide__wrapper .search-form__submit .icon--search {
  position: absolute;
  inset-block-start: 0;
  inset-inline-end: 0;
  display: block;
  width: 1.5rem; /* Width of the SVG background image. */
  height: 100%;
  pointer-events: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='26' height='27.2' viewBox='0 0 26 27.2'%3e  %3cpath fill='%23fff' d='M25.8,25.5l-5.3-5.3c2.1-2.1,3.4-5.1,3.4-8.3C23.9,5.3,18.5,0,11.9,0C5.3,0,0,5.3,0,11.9c0,6.6,5.3,11.9,11.9,11.9c2.6,0,5.1-0.9,7-2.3l5.4,5.4c0.4,0.4,1,0.4,1.4,0C26.1,26.6,26.1,25.9,25.8,25.5z M11.9,21.9c-5.5,0-9.9-4.4-9.9-9.9S6.4,2,11.9,2c5.5,0,9.9,4.4,9.9,9.9S17.4,21.9,11.9,21.9z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.block-search-wide__wrapper .search-form__submit .icon--search::after {
  position: absolute;
  inset-block-end: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 0;
  content: "";
  transition: transform 0.2s;
  transform: scaleX(0);
  transform-origin: left;
  border-block-start: solid var(--sp0-5) var(--color--primary-50);
}

.block-search-wide__wrapper .search-form__submit:focus {
  outline: solid 4px transparent;
  outline-offset: -4px;
  box-shadow: none;
}

.block-search-wide__wrapper .search-form__submit:focus span::after {
  transform: scaleX(1);
}

@media screen and (-ms-high-contrast: active) {
  .block-search-wide__wrapper .search-form__submit:focus {
    border-bottom-width: var(--sp0-5);
  }

  .block-search-wide__wrapper .search-form__submit:focus span::after {
    content: none;
  }
}

@media screen and (-ms-high-contrast: active) {
  /* Edge's high contrast does show the background image, so we hide it. */
  .block-search-wide__wrapper .search-form__submit .icon--search {
    display: none;
  }
}

.block-search-wide__container {
  max-width: var(--max-width);
  padding-inline-end: var(--sp2);
}

.block-search-wide__grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-col-count), 1fr);
  grid-column-gap: var(--grid-gap);
}

/* Override specificity from container-inline.module.css */

.container-inline .block-search-wide__container {
  display: block;
}

.container-inline .block-search-wide__grid {
  display: grid;
}

.block-search-wide__button {
  position: relative;
  display: none;
  width: var(--sp3);
  height: var(--sp6);
  cursor: pointer;
  color: var(--color-text-neutral-loud); /* Affects SVG search icon. */
  border: 0;
  background: transparent;
  -webkit-appearance: none;
}

.block-search-wide__button:focus {
  position: relative;
  outline: 0;
}

.block-search-wide__button:focus::after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: var(--sp3);
  content: "";
  transform: translate(-50%, -50%);
  border: solid 2px var(--color--primary-50);
  border-radius: 0.25rem;
}

.block-search-wide__button[aria-expanded="true"] {
  background: var(--color--black);
}

.block-search-wide__button[aria-expanded="true"]:focus::after {
  border-color: var(--color--white);
}

.block-search-wide__button[aria-expanded="true"] .block-search-wide__button-close::before,
.block-search-wide__button[aria-expanded="true"] .block-search-wide__button-close::after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: var(--sp1-5);
  height: 0;
  content: "";
  border-block-start: solid 2px var(--color--white);
}

.block-search-wide__button[aria-expanded="true"] .block-search-wide__button-close::before {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.block-search-wide__button[aria-expanded="true"] .block-search-wide__button-close::after {
  transform: translate(-50%, -50%) rotate(45deg);
}

.block-search-wide__button[aria-expanded="true"] svg {
  display: none;
}

.block-search-wide__button svg {
  margin-inline-start: auto;
  margin-inline-end: auto;
}

@media (forced-colors: active) {
  .block-search-wide__button {
    background: ButtonFace;
  }

  .block-search-wide__button path {
    fill: ButtonText;
  }
}

/* Provide rudimentary access to site search if JS is disabled. */

html:not(.js) .search-block-form:focus-within .block-search-wide__wrapper {
  visibility: visible;
  max-height: var(--sp8);
}

/* Necessary to override specificity of transpiled PostCSS properties from default input focus styling. */

[dir] .block-search-wide__wrapper input[type="search"] {
  background-color: transparent;
  background-image: linear-gradient(var(--color--primary-50), var(--color--primary-50)); /* Two values are needed for IE11 support. */
  background-repeat: no-repeat;
  background-position: bottom left; /* LTR */
  background-size: 0% 0.625rem;
}

[dir] .block-search-wide__wrapper input[type="search"]:focus {
  background-size: 100% var(--sp0-5);
}

[dir="rtl"] .block-search-wide__wrapper input[type="search"] {
  background-position: bottom right;
}

[dir="rtl"] .block-search-wide__wrapper .search-form__submit .icon--search::after {
  transform-origin: right;
}

@media (min-width: 75rem) {
  body:not(.is-always-mobile-nav) .block-search-wide__wrapper,
  body:not(.is-always-mobile-nav) .block-search-wide__button {
    display: block;
  }
}
