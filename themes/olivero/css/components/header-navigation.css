/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Navigation in header.
 */

.header-nav {
  position: fixed;
  z-index: 501; /* Appear above overlay and contextual links in header. */
  inset-block-start: 0;
  inset-inline-start: 100%;
  visibility: hidden;
  overflow: auto;
  /* Ensure that header nav not use additional space and force system branding
   * block text to unnecessarily wrap. */
  flex-basis: max-content;
  width: 100%;
  max-width: var(--mobile-nav-width);
  height: 100%;
  padding-block: 0 var(--sp);
  padding-inline-start: var(--sp);
  padding-inline-end: var(--sp);
  /* Create room for the "close" button. We cannot use margin because the
   * mobile navigation needs to slide beneath the button, but we also cannot
   * use padding because that would enable the button to scroll out of the
   * viewport on short screens. */
  border-block-start: solid var(--color--white) calc(var(--sp3) + var(--drupal-displace-offset-top, 0px));
  background-color: var(--color--white);
  box-shadow: 0 0 72px rgba(0, 0, 0, 0.1);
}

.header-nav.is-active {
  visibility: visible;
  transform: translateX(calc(-100% - var(--drupal-displace-offset-right, 0px))); /* LTR */
}

.header-nav.is-active:dir(rtl) {
  transform: translateX(calc(100% + var(--drupal-displace-offset-left, 0px)));
}

@media (min-width: 31.25rem) {
  .header-nav {
    border-top-width: calc(var(--sp5) + var(--drupal-displace-offset-top, 0px));
  }
}

@media (min-width: 43.75rem) {
  .header-nav {
    padding-block-end: var(--sp3);
    padding-inline-start: var(--sp3);
    border-top-width: calc(var(--sp7) + var(--drupal-displace-offset-top, 0px));
  }
}

@media (min-width: 62.5rem) {
  .header-nav {
    grid-column: 5 / 14;
  }
}

/*
   * Ensure top border has the same color as the background when in forced colors.
   */

@media (forced-colors: active) {
  .header-nav {
    border-top-color: canvas;
  }
}

/*
 * Only apply transition styles when JS is loaded. This
 * works around https://bugs.chromium.org/p/chromium/issues/detail?id=332189
 */

html.js .header-nav {
  transition:
    visibility 0.2s,
    transform 0.2s;
}

@media (min-width: 75rem) {
  body:not(.is-always-mobile-nav) .header-nav {
    position: static;
    display: flex;
    visibility: visible;
    overflow: visible;
    grid-column: 5 / 15;
    align-items: center;
    justify-content: flex-end;
    max-width: none;
    height: var(--header-height-wide-when-fixed);
    margin-block-start: auto;
    padding-block: 0;
    padding-inline-start: 0;
    padding-inline-end: 0;
    transition: transform 0.2s;
    transform: none;
    border-block-start: 0;
    box-shadow: none;
  }
}

@media (min-width: 75rem) {
  body.is-always-mobile-nav .header-nav {
    overflow: auto;
    max-width: calc((7 * (var(--grid-col-width) + var(--grid-gap))));
    padding-inline-end: var(--sp);
    transition:
      transform 0.2s,
      visibility 0.2s;
    border-top-width: calc(var(--drupal-displace-offset-top, 0px) + var(--sp11));
  }
}

@media (min-width: 90rem) {
  body.is-always-mobile-nav .header-nav {
    max-width: calc(100vw - (var(--max-width) + var(--content-left)) + ((7 * (var(--grid-col-width) + var(--grid-gap)))));
    padding-inline-end: calc(100vw - (var(--max-width) + var(--content-left) - var(--sp)));
  }
}

.header-nav-overlay {
  position: fixed;
  z-index: 101;
  inset-block-start: 0;
  inset-inline-start: 0;
  display: none;
  width: 100%;
  height: 100vh;
  opacity: 0.2;
  background: var(--color--gray-5);
}

@media (forced-colors: active) {
  .header-nav-overlay {
    background: canvastext;
  }
}

.is-overlay-active .header-nav-overlay {
  display: block;
}
