/**
 * @file
 * Skip link
 *
 * Allows keyboard users to quickly skip to the main content of the page.
 */

@import "../base/media-queries.pcss.css";

.skip-link {
  display: block;
  width: 100%;
  max-width: calc(var(--max-bg-color) + var(--drupal-displace-offset-left, 0px));
  padding-block: var(--sp0-5);
  padding-inline-start: var(--sp);
  padding-inline-end: var(--sp);
  text-decoration: none;
  color: var(--color--white);
  outline: 0;
  background-color: var(--color--gray-5);

  &:hover {
    text-decoration: underline;
    color: var(--color--white);
  }

  &::after {
    content: "\0020	➔";
  }
}

.skip-link.focusable:focus {
  position: absolute !important; /* Override position from module file. */
  z-index: 503;
  width: 100%;
  height: 40px;
  outline: none;
}
