/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Footer regions.
 */

.site-footer {
  position: relative; /* stack above left social bar */
  color: var(--color--gray-65);
  background: linear-gradient(180deg, var(--color--gray-5) 0%, var(--color--gray-10) 100%);
}

.site-footer .menu {
  margin-inline-start: 0;
  list-style: none;
}

.site-footer .menu ul {
  margin-inline-start: var(--sp);
}

.site-footer .menu li {
  margin-block-end: var(--sp0-5);
}

.site-footer a {
  color: inherit;
}

.site-footer a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

@media (min-width: 75rem) {
  body:not(.is-always-mobile-nav) .site-footer {
    border-inline-start: solid var(--content-left) var(--color--black);
  }
}
