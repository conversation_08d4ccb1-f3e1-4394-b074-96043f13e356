{#
/**
 * @file
 * Theme override for a link to a file.
 *
 * Available variables:
 * - attributes: The HTML attributes for the containing element.
 * - link: A link to the file.
 * - icon: The icon image representing the file type.
 * - file_size: The size of the file.
 *
 * @see template_preprocess_file_link()
 */
#}
{{ attach_library('starterkit_theme/file') }}
<span{{ attributes }}>{{ icon }} {{ link }}</span>
