/**
 * @file
 * Styling for the shortcut module.
 */

/**
 * Toolbar.
 */
.toolbar .toolbar-tray-vertical .edit-shortcuts {
  padding: 1em;
  text-align: right; /* LTR */
}
[dir="rtl"] .toolbar .toolbar-tray-vertical .edit-shortcuts {
  text-align: left;
}
.toolbar .toolbar-tray-horizontal .edit-shortcuts {
  float: right; /* LTR */
}
[dir="rtl"] .toolbar .toolbar-tray-horizontal .edit-shortcuts {
  float: left;
}

/**
 * Add/remove links.
 */
.shortcut-action {
  display: inline-block;
  margin-left: 0.3em; /* LTR */
}
[dir="rtl"] .shortcut-action {
  margin-right: 0.3em;
  margin-left: 0;
}
.shortcut-action__message {
  display: inline-block;
  margin-left: 0.3em; /* LTR */
  padding: 0 5px;
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  -webkit-transform: translateY(-12px);
  transform: translateY(-12px);
  opacity: 0;
  color: #fff;
  border-radius: 5px;
  background: #000;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
[dir="rtl"] .shortcut-action__message {
  margin-right: 0.3em;
  margin-left: 0;
}
.shortcut-action:hover .shortcut-action__message,
.shortcut-action:focus .shortcut-action__message {
  -webkit-transform: translateY(-2px);
  transform: translateY(-2px);
  opacity: 1;
}
