<?php

/**
 * @file
 * The jQuery Superfish plugin for Drupal menus.
 */

use <PERSON><PERSON>al\Component\Utility\Unicode;
use Drupal\Core\Block\BlockPluginInterface;
use <PERSON>upal\Core\Config\BootstrapConfigStorageFactory;
use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_block_view_BASE_BLOCK_ID_alter().
 */
function superfish_block_view_superfish_alter(array &$build, BlockPluginInterface $block) {
  $build['#contextual_links']['menu'] = [
    'route_parameters' => ['menu' => $block->getDerivativeId()],
  ];
}

/**
 * Implements hook_libraries_info().
 */
function superfish_libraries_info() {
  $libraries['superfish'] = [
    'name' => 'superfish',
    'vendor url' => 'https://github.com/lobsterr/drupal-superfish',
    'download url' => 'https://github.com/lobsterr/drupal-superfish/zipball/2.x',
    'version callback' => 'superfish_library_version',
    'files' => [
      'js' => [
        'superfish.js',
        'jquery.hoverIntent.minified.js',
        'sfsmallscreen.js',
        'sftouchscreen.js',
        'supersubs.js',
        'supposition.js',
      ],
      'css' => [
        'css/superfish.css',
        'style/black/black.css',
        'style/blue/blue.css',
        'style/coffee/coffee.css',
        'style/default/default.css',
        'style/white/white.css',
      ],
    ],
  ];
  return $libraries;
}

/**
 * Implements hook_library_info_build().
 */
function superfish_library_info_build() {
  $libraries = [];
  if ($superfish_library_path = superfish_library_path()) {
    $superfish_library_path = '/' . $superfish_library_path;
    $libraries = [
      'superfish' => [
        'remote' => 'https://github.com/lobsterr/drupal-superfish',
        'version' => '2.0',
        'license' => [
          'name' => 'MIT',
          'url' => 'https://www.gnu.org/licenses/old-licenses/gpl-2.0.en.html',
          'gpl-compatible' => FALSE,
        ],
        'js' => [
          'js/superfish.js' => [],
        ],
        'dependencies' => [
          'core/jquery',
          'core/drupal',
          'core/drupalSettings',
          'core/once',
        ],
      ],
      'init' => [
        'js' => [
          $superfish_library_path . '/superfish.js' => [],
        ],
        'css' => [
          'base' => [
            $superfish_library_path . '/css/superfish.css' => [],
          ],
        ],
      ],
      'superfish_hoverintent' => [
        'js' => [
          $superfish_library_path . '/jquery.hoverIntent.minified.js' => [
            'minified' => TRUE,
          ],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_smallscreen' => [
        'js' => [
          $superfish_library_path . '/sfsmallscreen.js' => [],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_touchscreen' => [
        'js' => [
          $superfish_library_path . '/sftouchscreen.js' => [],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_supersubs' => [
        'js' => [
          $superfish_library_path . '/supersubs.js' => [],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_supposition' => [
        'js' => [
          $superfish_library_path . '/supposition.js' => [],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_style_black' => [
        'css' => [
          'theme' => [
            $superfish_library_path . '/style/black/black.css' => [],
          ],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_style_blue' => [
        'css' => [
          'theme' => [
            $superfish_library_path . '/style/blue/blue.css' => [],
          ],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_style_coffee' => [
        'css' => [
          'theme' => [
            $superfish_library_path . '/style/coffee/coffee.css' => [],
          ],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_style_default' => [
        'css' => [
          'theme' => [
            $superfish_library_path . '/style/default/default.css' => [],
          ],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
      'superfish_style_white' => [
        'css' => [
          'theme' => [
            $superfish_library_path . '/style/white/white.css' => [],
          ],
        ],
        'dependencies' => [
          'superfish/init',
        ],
      ],
    ];
    if ($easing_library_path = superfish_library_path('easing')) {
      $easing_library_path = '/' . $easing_library_path;
      $libraries += [
        'superfish_easing' => [
          'js' => [
            $easing_library_path . '/jquery.easing.js' => [],
          ],
        ],
      ];
    }
  }
  return $libraries;
}

/**
 * Returns the Superfish library folder location.
 */
function superfish_library_path($library = 'drupal-superfish') {
  $directory = FALSE;
  // Ensure the Libraries API module is installed and working.
  if (\Drupal::hasService('library.libraries_directory_file_finder')) {
    $directory = \Drupal::service('library.libraries_directory_file_finder')->find($library);
    if ($directory) {
      return $directory;
    }
  }
  // Otherwise, use the default directory.
  if (\Drupal::hasContainer()) {
    $profile = \Drupal::installProfile();
  }
  else {
    $profile = BootstrapConfigStorageFactory::getDatabaseStorage()->read('core.extension')['profile'];
  }

  $profile_library_sub_path = $profile . '/libraries/' . $library;
  $paths = [
    'profiles/' . $profile_library_sub_path,
    'profiles/contrib/' . $profile_library_sub_path,
    'profiles/custom/' . $profile_library_sub_path,
    'libraries/' . $library,
    'sites/all/libraries/' . $library,
    'sites/default/libraries/' . $library,
  ];

  // Convert relative paths to absolute real paths and check existence.
  foreach ($paths as $relative_path) {
    $absolute_path = DRUPAL_ROOT . '/' . $relative_path;
    if (file_exists($absolute_path)) {
      $directory = $absolute_path;
      break;
    }
  }

  if (!$directory && Unicode::ucfirst($library) !== $library) {
    $directory = superfish_library_path(Unicode::ucfirst($library));
  }
  return $directory;
}

/**
 * Verifies Superfish library is present.
 */
function superfish_library_check() {
  $directory = superfish_library_path();
  if ($directory && file_exists($directory . '/superfish.js')) {
    return TRUE;
  }
  return FALSE;
}

/**
 * Checks Superfish library version.
 */
function superfish_library_version() {
  // Get the library version.
  if (($directory = superfish_library_path()) && file_exists($directory . '/VERSION')) {
    $version = file_get_contents($directory . '/VERSION');
    if ($version !== FALSE) {
      // Removing blank lines and white spaces.
      $version = preg_replace("/(^[\r\n]*|[\r\n]+)[\s\t]*[\r\n]+/", "\n", trim($version));
      if (!empty($version)) {
        return $version;
      }
    }
  }
  return FALSE;
}

/**
 * Generate a list of available slide-in effects.
 */
function superfish_effects() {
  $output = [
    'none' => '- ' . t('None') . ' -',
    'vertical' => t('Vertical'),
    'horizontal' => t('Horizontal'),
    'diagonal' => t('Diagonal'),
  ];

  if (superfish_library_path('easing')) {
    $easing_types = [
      'easeInSine_vertical' => 'easeInSine (' . t('Vertical') . ')',
      'easeInSine_horizontal' => 'easeInSine (' . t('Horizontal') . ')',
      'easeInSine_diagonal' => 'easeInSine (' . t('Diagonal') . ')',
      'easeInQuint_vertical' => 'easeInQuint (' . t('Vertical') . ')',
      'easeInQuint_horizontal' => 'easeInQuint (' . t('Horizontal') . ')',
      'easeInQuint_diagonal' => 'easeInQuint (' . t('Diagonal') . ')',
      'easeInQuart_vertical' => 'easeInQuart (' . t('Vertical') . ')',
      'easeInQuart_horizontal' => 'easeInQuart (' . t('Horizontal') . ')',
      'easeInQuart_diagonal' => 'easeInQuart (' . t('Diagonal') . ')',
      'easeInQuad_vertical' => 'easeInQuad (' . t('Vertical') . ')',
      'easeInQuad_horizontal' => 'easeInQuad (' . t('Horizontal') . ')',
      'easeInQuad_diagonal' => 'easeInQuad (' . t('Diagonal') . ')',
      'easeInExpo_vertical' => 'easeInExpo (' . t('Vertical') . ')',
      'easeInExpo_horizontal' => 'easeInExpo (' . t('Horizontal') . ')',
      'easeInExpo_diagonal' => 'easeInExpo (' . t('Diagonal') . ')',
      'easeInElastic_vertical' => 'easeInElastic (' . t('Vertical') . ')',
      'easeInElastic_horizontal' => 'easeInElastic (' . t('Horizontal') . ')',
      'easeInElastic_diagonal' => 'easeInElastic (' . t('Diagonal') . ')',
      'easeInCubic_vertical' => 'easeInCubic (' . t('Vertical') . ')',
      'easeInCubic_horizontal' => 'easeInCubic (' . t('Horizontal') . ')',
      'easeInCubic_diagonal' => 'easeInCubic (' . t('Diagonal') . ')',
      'easeInCirc_vertical' => 'easeInCirc (' . t('Vertical') . ')',
      'easeInCirc_horizontal' => 'easeInCirc (' . t('Horizontal') . ')',
      'easeInCirc_diagonal' => 'easeInCirc (' . t('Diagonal') . ')',
      'easeInBounce_vertical' => 'easeInBounce (' . t('Vertical') . ')',
      'easeInBounce_horizontal' => 'easeInBounce (' . t('Horizontal') . ')',
      'easeInBounce_diagonal' => 'easeInBounce (' . t('Diagonal') . ')',
      'easeInBack_vertical' => 'easeInBack (' . t('Vertical') . ')',
      'easeInBack_horizontal' => 'easeInBack (' . t('Horizontal') . ')',
      'easeInBack_diagonal' => 'easeInBack (' . t('Diagonal') . ')',
      'easeOutSine_vertical' => 'easeOutSine (' . t('Vertical') . ')',
      'easeOutSine_horizontal' => 'easeOutSine (' . t('Horizontal') . ')',
      'easeOutSine_diagonal' => 'easeOutSine (' . t('Diagonal') . ')',
      'easeOutQuint_vertical' => 'easeOutQuint (' . t('Vertical') . ')',
      'easeOutQuint_horizontal' => 'easeOutQuint (' . t('Horizontal') . ')',
      'easeOutQuint_diagonal' => 'easeOutQuint (' . t('Diagonal') . ')',
      'easeOutQuart_vertical' => 'easeOutQuart (' . t('Vertical') . ')',
      'easeOutQuart_horizontal' => 'easeOutQuart (' . t('Horizontal') . ')',
      'easeOutQuart_diagonal' => 'easeOutQuart (' . t('Diagonal') . ')',
      'easeOutQuad_vertical' => 'easeOutQuad (' . t('Vertical') . ')',
      'easeOutQuad_horizontal' => 'easeOutQuad (' . t('Horizontal') . ')',
      'easeOutQuad_diagonal' => 'easeOutQuad (' . t('Diagonal') . ')',
      'easeOutExpo_vertical' => 'easeOutExpo (' . t('Vertical') . ')',
      'easeOutExpo_horizontal' => 'easeOutExpo (' . t('Horizontal') . ')',
      'easeOutExpo_diagonal' => 'easeOutExpo (' . t('Diagonal') . ')',
      'easeOutElastic_vertical' => 'easeOutElastic (' . t('Vertical') . ')',
      'easeOutElastic_horizontal' => 'easeOutElastic (' . t('Horizontal') . ')',
      'easeOutElastic_diagonal' => 'easeOutElastic (' . t('Diagonal') . ')',
      'easeOutCubic_vertical' => 'easeOutCubic (' . t('Vertical') . ')',
      'easeOutCubic_horizontal' => 'easeOutCubic (' . t('Horizontal') . ')',
      'easeOutCubic_diagonal' => 'easeOutCubic (' . t('Diagonal') . ')',
      'easeOutCirc_vertical' => 'easeOutCirc (' . t('Vertical') . ')',
      'easeOutCirc_horizontal' => 'easeOutCirc (' . t('Horizontal') . ')',
      'easeOutCirc_diagonal' => 'easeOutCirc (' . t('Diagonal') . ')',
      'easeOutBounce_vertical' => 'easeOutBounce (' . t('Vertical') . ')',
      'easeOutBounce_horizontal' => 'easeOutBounce (' . t('Horizontal') . ')',
      'easeOutBounce_diagonal' => 'easeOutBounce (' . t('Diagonal') . ')',
      'easeOutBack_vertical' => 'easeOutBack (' . t('Vertical') . ')',
      'easeOutBack_horizontal' => 'easeOutBack (' . t('Horizontal') . ')',
      'easeOutBack_diagonal' => 'easeOutBack (' . t('Diagonal') . ')',
    ];
    $output = array_merge($output, $easing_types);
  }
  return $output;
}

/**
 * Helper function to clean up arrays.
 */
function superfish_array_filter($haystack) {
  foreach ($haystack as $key => $value) {
    if (is_array($value)) {
      $haystack[$key] = superfish_array_filter($haystack[$key]);
    }
    elseif (empty($value) && is_bool($value) !== TRUE) {
      if ($haystack[$key] != '0') {
        unset($haystack[$key]);
      }
    }
  }
  return $haystack;
}

/**
 * Implements hook_theme().
 */
function superfish_theme($existing, $type, $theme, $path) {
  return [
    'superfish' => [
      'render element' => 'element',
      'file' => 'superfish.theme.inc',
    ],
    'superfish_menu_items' => [
      'render element' => 'element',
      'file' => 'superfish.theme.inc',
    ],
    'superfish_help' => [
      'variables' => [
        'title' => NULL,
        'content' => NULL,
      ],
      'template' => 'superfish--help',
    ],
  ];
}

/**
 * Implements hook_help().
 */
function superfish_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.superfish':
      $output = [
        '#theme' => 'superfish_help',
        '#title' => t('About Superfish'),
        '#content' => t('<a href="@url_module">Superfish module</a> integrates <a href="@url_library">jQuery Superfish</a> plugin to your Drupal menu blocks. Please refer to the module <a href="@url_documentation">documentation</a> for more information.',
          [
            '@url_library' => 'https://github.com/mehrpadin/Superfish-for-Drupal',
            '@url_module' => 'https://www.drupal.org/project/superfish',
            '@url_documentation' => 'https://www.drupal.org/node/1125896',
          ]
        ),
      ];
      return $output;
  }
}
