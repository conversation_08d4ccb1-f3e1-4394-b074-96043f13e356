<?php

/**
 * @file
 */

use Drupal\Core\Form\FormStateInterface;
use Drupal\imageapi_optimize\Entity\ImageAPIOptimizePipeline;

/**
 * Gets an array of image pipelines suitable for using as select list options.
 *
 * @param $include_empty
 *   If TRUE a '- None -' option will be inserted in the options array.
 *
 * @return
 *   Array of image pipelines both key and value are set to pipeline name.
 */
function imageapi_optimize_pipeline_options($include_empty = TRUE, $include_site_default = TRUE) {
  $pipelines = ImageAPIOptimizePipeline::loadMultiple();
  $options = [];
  if ($include_empty && !empty($pipelines)) {
    $options[''] = t('- None -');
  }
  if ($include_site_default && !empty($pipelines)) {
    if ($default_pipeline_name = \Drupal::config('imageapi_optimize.settings')->get('default_pipeline')) {
      if ($default_pipeline = ImageAPIOptimizePipeline::load($default_pipeline_name)) {
        $options['__default__'] = t('Sitewide default pipeline: @name', ['@name' => $default_pipeline->label()]);
      }
    }
  }
  foreach ($pipelines as $name => $pipeline) {
    $options[$name] = $pipeline->label();
  }

  if (empty($options)) {
    $options[''] = t('No defined pipelines');
  }
  return $options;
}

/**
 * Implements hook_entity_type_alter().
 */
function imageapi_optimize_entity_type_alter(array &$entity_types) {
  return \Drupal::service('imageapi_optimize.hooks')->entity_type_alter($entity_types);
}

/**
 * Implements hook_config_schema_info_alter().
 */
function imageapi_optimize_config_schema_info_alter(&$definitions) {
  return \Drupal::service('imageapi_optimize.hooks')->config_schema_info_alter($definitions);
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function imageapi_optimize_form_image_style_edit_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  return \Drupal::service('imageapi_optimize.hooks')->form_image_style_edit_form_alter($form, $form_state, $form_id);
}
