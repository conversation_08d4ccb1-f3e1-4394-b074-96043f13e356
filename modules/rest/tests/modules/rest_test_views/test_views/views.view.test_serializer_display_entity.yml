langcode: en
status: true
dependencies:
  module:
    - rest
    - user
id: test_serializer_display_entity
label: 'Test serialize display entity rows'
module: rest
description: ''
tag: ''
base_table: entity_test
base_field: id
display:
  default:
    display_plugin: default
    id: default
    display_title: Default
    position: null
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
      query:
        type: views_query
      exposed_form:
        type: basic
      style:
        type: serializer
      row:
        type: data_entity
      sorts:
        id:
          id: standard
          table: entity_test
          field: id
          order: DESC
          plugin_id: date
          entity_type: entity_test
          entity_field: id
      title: 'Test serialize'
      arguments: {  }
  rest_export_1:
    display_plugin: rest_export
    id: rest_export_1
    display_title: serializer
    position: null
    display_options:
      defaults:
        access: false
      path: test/serialize/entity
