langcode: en
status: true
dependencies:
  module:
    - taxonomy
    - user
id: test_taxonomy_parent
label: test_taxonomy_parent
module: views
description: ''
tag: ''
base_table: taxonomy_term_data
base_field: tid
display:
  default:
    display_plugin: default
    id: default
    display_title: Default
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: full
        options:
          items_per_page: 10
          offset: 0
          id: 0
          total_pages: null
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          tags:
            previous: '‹ Previous'
            next: 'Next ›'
            first: '« First'
            last: 'Last »'
          quantity: 9
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: fields
        options:
          inline: {  }
          separator: ''
          hide_empty: false
          default_field_elements: true
      fields:
        name:
          id: name
          table: taxonomy_term_data
          field: name
          label: ''
          alter:
            alter_text: false
            make_link: false
            absolute: false
            trim: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            html: false
          hide_empty: false
          empty_zero: false
          relationship: none
          group_type: group
          admin_label: ''
          exclude: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_alter_empty: true
          plugin_id: field
          type: string
          settings:
            link_to_entity: true
          entity_type: taxonomy_term
          entity_field: name
      filters: {  }
      sorts: {  }
      header: {  }
      footer: {  }
      empty: {  }
      relationships:
        parent:
          id: parent
          table: taxonomy_term__parent
          field: parent_target_id
          relationship: none
          group_type: group
          admin_label: Parent
          required: true
          plugin_id: standard
      arguments: {  }
