<?php

namespace Drupal\vocabulary_serialization_test;

use <PERSON><PERSON><PERSON>\Core\Cache\CacheableResponse;
use <PERSON><PERSON><PERSON>\taxonomy\VocabularyInterface;

class VocabularyResponse extends CacheableResponse {

  /**
   * @var \Drupal\taxonomy\VocabularyInterface
   */
  protected $vocabulary;

  public function setVocabulary(VocabularyInterface $vocabulary) {
    $this->vocabulary = $vocabulary;
  }

}
