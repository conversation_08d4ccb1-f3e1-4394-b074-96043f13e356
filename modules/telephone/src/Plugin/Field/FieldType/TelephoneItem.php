<?php

namespace Dr<PERSON>al\telephone\Plugin\Field\FieldType;

use <PERSON>upal\Core\Field\Attribute\FieldType;
use <PERSON>upal\Core\Field\FieldDefinitionInterface;
use <PERSON>upal\Core\Field\FieldItemBase;
use <PERSON>upal\Core\Field\FieldStorageDefinitionInterface;
use <PERSON>upal\Core\StringTranslation\TranslatableMarkup;
use Drupal\Core\TypedData\DataDefinition;

/**
 * Plugin implementation of the 'telephone' field type.
 */
#[FieldType(
  id: "telephone",
  label: new TranslatableMarkup("Telephone number"),
  description: new TranslatableMarkup("This field stores a telephone number."),
  default_widget: "telephone_default",
  default_formatter: "basic_string"
)]
class TelephoneItem extends FieldItemBase {

  /**
   * The maximum length for a telephone value.
   */
  const MAX_LENGTH = 256;

  /**
   * {@inheritdoc}
   */
  public static function schema(FieldStorageDefinitionInterface $field_definition) {
    return [
      'columns' => [
        'value' => [
          'type' => 'varchar',
          'length' => self::MAX_LENGTH,
        ],
      ],
    ];
  }

  /**
   * {@inheritdoc}
   */
  public static function propertyDefinitions(FieldStorageDefinitionInterface $field_definition) {
    $properties['value'] = DataDefinition::create('string')
      ->setLabel(new TranslatableMarkup('Telephone number'))
      ->setRequired(TRUE);

    return $properties;
  }

  /**
   * {@inheritdoc}
   */
  public function isEmpty() {
    $value = $this->get('value')->getValue();
    return $value === NULL || $value === '';
  }

  /**
   * {@inheritdoc}
   */
  public function getConstraints() {
    $constraint_manager = \Drupal::typedDataManager()->getValidationConstraintManager();
    $constraints = parent::getConstraints();

    $constraints[] = $constraint_manager->create('ComplexData', [
      'value' => [
        'Length' => [
          'max' => self::MAX_LENGTH,
          'maxMessage' => $this->t('%name: the telephone number may not be longer than @max characters.', ['%name' => $this->getFieldDefinition()->getLabel(), '@max' => self::MAX_LENGTH]),
        ],
      ],
    ]);

    return $constraints;
  }

  /**
   * {@inheritdoc}
   */
  public static function generateSampleValue(FieldDefinitionInterface $field_definition) {
    $values['value'] = rand(pow(10, 8), pow(10, 9) - 1);
    return $values;
  }

}
