<?php

/**
 * @file
 * Defines a simple telephone number field type.
 */

use Drupal\Core\Field\FieldTypeCategoryManagerInterface;
use Drupal\Core\Url;
use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function telephone_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.telephone':
      $output = '';
      $output .= '<h2>' . t('About') . '</h2>';
      $output .= '<p>' . t('The Telephone module allows you to create fields that contain telephone numbers. See the <a href=":field">Field module help</a> and the <a href=":field_ui">Field UI help</a> pages for general information on fields and how to create and manage them. For more information, see the <a href=":telephone_documentation">online documentation for the Telephone module</a>.', [':field' => Url::fromRoute('help.page', ['name' => 'field'])->toString(), ':field_ui' => (\Drupal::moduleHandler()->moduleExists('field_ui')) ? Url::fromRoute('help.page', ['name' => 'field_ui'])->toString() : '#', ':telephone_documentation' => 'https://www.drupal.org/documentation/modules/telephone']) . '</p>';
      $output .= '<h2>' . t('Uses') . '</h2>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Managing and displaying telephone fields') . '</dt>';
      $output .= '<dd>' . t('The <em>settings</em> and the <em>display</em> of the telephone field can be configured separately. See the <a href=":field_ui">Field UI help</a> for more information on how to manage fields and their display.', [':field_ui' => (\Drupal::moduleHandler()->moduleExists('field_ui')) ? Url::fromRoute('help.page', ['name' => 'field_ui'])->toString() : '#']) . '</dd>';
      $output .= '<dt>' . t('Displaying telephone numbers as links') . '</dt>';
      $output .= '<dd>' . t('Telephone numbers can be displayed as links with the scheme name <em>tel:</em> by choosing the <em>Telephone</em> display format on the <em>Manage display</em> page. Any spaces will be stripped out of the link text. This semantic markup improves the user experience on mobile and assistive technology devices.') . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_field_formatter_info_alter().
 */
function telephone_field_formatter_info_alter(&$info) {
  $info['string']['field_types'][] = 'telephone';
}

/**
 * Implements hook_field_type_category_info_alter().
 */
function telephone_field_type_category_info_alter(&$definitions) {
  // The `telephone` field type belongs in the `general` category, so the
  // libraries need to be attached using an alter hook.
  $definitions[FieldTypeCategoryManagerInterface::FALLBACK_CATEGORY]['libraries'][] = 'telephone/drupal.telephone-icon';
}
