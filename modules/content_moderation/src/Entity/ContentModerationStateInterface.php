<?php

namespace Drupal\content_moderation\Entity;

use <PERSON>upal\Core\Entity\ContentEntityInterface;
use <PERSON><PERSON><PERSON>\user\EntityOwnerInterface;

/**
 * An interface for Content moderation state entity.
 *
 * Content moderation state entities track the moderation state of other content
 * entities.
 *
 * @internal
 */
interface ContentModerationStateInterface extends ContentEntityInterface, EntityOwnerInterface {

}
