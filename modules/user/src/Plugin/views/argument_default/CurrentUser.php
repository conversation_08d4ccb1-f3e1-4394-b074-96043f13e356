<?php

namespace Dr<PERSON>al\user\Plugin\views\argument_default;

use <PERSON><PERSON>al\Core\Cache\Cache;
use <PERSON><PERSON>al\Core\Cache\CacheableDependencyInterface;
use <PERSON>upal\Core\StringTranslation\TranslatableMarkup;
use Drupal\views\Attribute\ViewsArgumentDefault;
use Drupal\views\Plugin\views\argument_default\ArgumentDefaultPluginBase;

/**
 * Default argument plugin to extract the current user.
 *
 * This plugin actually has no options so it does not need to do a great deal.
 */
#[ViewsArgumentDefault(
  id: 'current_user',
  title: new TranslatableMarkup('User ID from logged in user'),
)]
class CurrentUser extends ArgumentDefaultPluginBase implements CacheableDependencyInterface {

  /**
   * {@inheritdoc}
   */
  public function getArgument() {
    return \Drupal::currentUser()->id();
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheMaxAge() {
    return Cache::PERMANENT;
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheContexts() {
    return ['user'];
  }

}
