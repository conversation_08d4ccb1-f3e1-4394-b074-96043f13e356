<?php

/**
 * @file
 * Helper module for the Text Editor tests.
 */

use Dr<PERSON>al\Core\Entity\EntityInterface;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON><PERSON>\filter\FilterFormatInterface;
use <PERSON><PERSON>al\file\FileInterface;

/**
 * Implements hook_entity_update().
 *
 * @see \Drupal\Tests\editor\Kernel\EntityUpdateTest
 */
function editor_test_entity_update(EntityInterface $entity) {
  // Only act on nodes.
  if (!$entity instanceof NodeInterface) {
    return;
  }

  // Avoid infinite loop by only going through our post save logic once.
  if (!empty($entity->editor_test_updating)) {
    return;
  }

  // Set flag for whether or not the entity needs to be resaved.
  $needs_update = FALSE;

  // Perform our post save logic.
  if ($entity->title->value == 'test updated') {
    // Change the node title.
    $entity->title->value = 'test updated 2';
    $needs_update = TRUE;
  }

  if ($needs_update) {
    // Set flag on entity that our logic was already executed.
    $entity->editor_test_updating = TRUE;
    // And resave entity.
    $entity->save();
  }
}

/**
 * Implements hook_editor_js_settings_alter().
 */
function editor_test_editor_js_settings_alter(&$settings) {
  // Allow tests to enable or disable this alter hook.
  if (!\Drupal::state()->get('editor_test_js_settings_alter_enabled', FALSE)) {
    return;
  }

  if (isset($settings['editor']['formats']['full_html'])) {
    $settings['editor']['formats']['full_html']['editorSettings']['ponyModeEnabled'] = FALSE;
  }
}

/**
 * Implements hook_editor_xss_filter_alter().
 */
function editor_test_editor_xss_filter_alter(&$editor_xss_filter_class, FilterFormatInterface $format, ?FilterFormatInterface $original_format = NULL) {
  // Allow tests to enable or disable this alter hook.
  if (!\Drupal::keyValue('editor_test')->get('editor_xss_filter_alter_enabled', FALSE)) {
    return;
  }

  $filters = $format->filters()->getAll();
  if (isset($filters['filter_html']) && $filters['filter_html']->status) {
    $editor_xss_filter_class = '\Drupal\editor_test\EditorXssFilter\Insecure';
  }
}

/**
 * Implements hook_editor_info_alter().
 */
function editor_test_editor_info_alter(&$items) {
  if (!\Drupal::state()->get('editor_test_give_me_a_trex_thanks', FALSE)) {
    unset($items['trex']);
  }
}

/**
 * Implements hook_ENTITY_TYPE_presave() for file entities.
 */
function editor_test_file_presave(FileInterface $file) {
  // Use state to keep track of how many times a file is saved.
  $file_save_count = \Drupal::state()->get('editor_test.file_save_count', []);
  $file_save_count[$file->getFilename()] = isset($file_save_count[$file->getFilename()]) ? $file_save_count[$file->getFilename()] + 1 : 1;
  \Drupal::state()->set('editor_test.file_save_count', $file_save_count);
}
