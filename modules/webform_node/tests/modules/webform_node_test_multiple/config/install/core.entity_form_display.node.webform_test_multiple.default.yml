langcode: en
status: true
dependencies:
  config:
    - field.field.node.webform_test_multiple.field_webform_test_multiple_a
    - field.field.node.webform_test_multiple.field_webform_test_multiple_b
    - node.type.webform_test_multiple
  module:
    - path
    - webform
id: node.webform_test_multiple.default
targetEntityType: node
bundle: webform_test_multiple
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_webform_test_multiple_a:
    weight: 32
    settings: {  }
    third_party_settings: {  }
    type: webform_entity_reference_select
    region: content
  field_webform_test_multiple_b:
    weight: 33
    settings: {  }
    third_party_settings: {  }
    type: webform_entity_reference_select
    region: content
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    settings:
      display_label: true
    weight: 15
    region: content
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    settings:
      display_label: true
    weight: 120
    region: content
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    settings:
      display_label: true
    weight: 16
    region: content
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    settings:
      match_operator: CONTAINS
      size: 60
      placeholder: ''
    region: content
    third_party_settings: {  }
hidden: {  }
