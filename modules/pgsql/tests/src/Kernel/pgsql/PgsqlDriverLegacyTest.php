<?php

declare(strict_types=1);

namespace Drupal\Tests\pgsql\Kernel\pgsql;

use Drupal\Core\Database\Driver\pgsql\Connection;
use Drupal\Core\Database\Driver\pgsql\Delete;
use Drupal\Core\Database\Driver\pgsql\Install\Tasks;
use Drupal\Core\Database\Driver\pgsql\Insert;
use Drupal\Core\Database\Driver\pgsql\Schema;
use Drupal\Core\Database\Driver\pgsql\Select;
use Drupal\Core\Database\Driver\pgsql\Truncate;
use Drupal\Core\Database\Driver\pgsql\Update;
use Drupal\Core\Database\Driver\pgsql\Upsert;
use Drupal\KernelTests\Core\Database\DriverSpecificDatabaseTestBase;
use Drupal\Tests\Core\Database\Stub\StubPDO;

/**
 * Tests the deprecations of the PostgreSQL database driver classes in Core.
 *
 * @group legacy
 * @group Database
 */
class PgsqlDriverLegacyTest extends DriverSpecificDatabaseTestBase {

  /**
   * @covers Dr<PERSON>al\Core\Database\Driver\pgsql\Install\Tasks
   */
  public function testDeprecationInstallTasks(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Install\Tasks is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $tasks = new Tasks();
    $this->assertInstanceOf(Tasks::class, $tasks);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Connection
   */
  public function testDeprecationConnection(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Connection is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $connection = new Connection($this->createMock(StubPDO::class), []);
    $this->assertInstanceOf(Connection::class, $connection);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Delete
   */
  public function testDeprecationDelete(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Delete is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $delete = new Delete($this->connection, 'test');
    $this->assertInstanceOf(Delete::class, $delete);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Insert
   */
  public function testDeprecationInsert(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Insert is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $insert = new Insert($this->connection, 'test');
    $this->assertInstanceOf(Insert::class, $insert);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Schema
   */
  public function testDeprecationSchema(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Schema is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $schema = new Schema($this->connection);
    $this->assertInstanceOf(Schema::class, $schema);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Select
   */
  public function testDeprecationSelect(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Select is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $select = new Select($this->connection, 'test');
    $this->assertInstanceOf(Select::class, $select);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Truncate
   */
  public function testDeprecationTruncate(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Truncate is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $truncate = new Truncate($this->connection, 'test');
    $this->assertInstanceOf(Truncate::class, $truncate);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Update
   */
  public function testDeprecationUpdate(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Update is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $update = new Update($this->connection, 'test');
    $this->assertInstanceOf(Update::class, $update);
  }

  /**
   * @covers Drupal\Core\Database\Driver\pgsql\Upsert
   */
  public function testDeprecationUpsert(): void {
    $this->expectDeprecation('\Drupal\Core\Database\Driver\pgsql\Upsert is deprecated in drupal:9.4.0 and is removed from drupal:11.0.0. The PostgreSQL database driver has been moved to the pgsql module. See https://www.drupal.org/node/3129492');
    $upsert = new Upsert($this->connection, 'test');
    $this->assertInstanceOf(Upsert::class, $upsert);
  }

}
