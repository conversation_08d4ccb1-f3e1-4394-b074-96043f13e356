langcode: en
status: true
dependencies:
  module:
    - node
id: test_store_pager_settings
label: test_store_pager_settings
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    display_options:
      access:
        type: none
      cache:
        type: tag
      exposed_form:
        type: basic
      pager:
        type: none
      style:
        type: default
      row:
        type: 'entity:node'
    display_plugin: default
    display_title: Default
    id: default
    position: 0
