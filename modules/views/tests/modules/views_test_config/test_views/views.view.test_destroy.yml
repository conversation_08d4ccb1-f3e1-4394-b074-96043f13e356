langcode: en
status: true
dependencies:
  module:
    - comment
    - node
    - user
id: test_destroy
label: test_destroy
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    display_options:
      access:
        type: none
      arguments:
        created_day:
          default_argument_type: fixed
          field: created_day
          id: created_day
          table: node_field_data
          plugin_id: date_day
          entity_type: node
        created_fulldate:
          default_argument_type: fixed
          field: created_fulldate
          id: created_fulldate
          table: node_field_data
          plugin_id: date_fulldate
          entity_type: node
        created_month:
          default_argument_type: fixed
          field: created_month
          id: created_month
          table: node_field_data
          plugin_id: date_month
          entity_type: node
      cache:
        type: tag
      empty:
        area:
          empty: false
          field: area
          id: area
          table: views
          plugin_id: text
        area_1:
          empty: false
          field: area
          id: area_1
          table: views
          plugin_id: text
      exposed_form:
        type: basic
      fields:
        created:
          field: created
          id: created
          table: node_field_data
          plugin_id: field
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
          entity_type: node
          entity_field: created
        nid:
          field: nid
          id: nid
          table: node_field_data
          plugin_id: field
          entity_type: node
          entity_field: nid
        path:
          field: view_node
          id: path
          table: node
          output_url_as_text: true
          plugin_id: entity_link
          entity_type: node
      filters:
        nid:
          id: nid
          table: node_field_data
          field: nid
          relationship: none
          group_type: group
          admin_label: ''
          operator: '='
          value:
            min: ''
            max: ''
            value: '0'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: numeric
          entity_type: node
          entity_field: nid
        status:
          field: '1'
          id: status
          table: node_field_data
          plugin_id: boolean
          entity_type: node
          entity_field: status
        title:
          field: title
          id: title
          table: node_field_data
          plugin_id: string
          entity_type: node
          entity_field: title
      footer:
        area:
          empty: false
          field: area
          id: area
          table: views
          plugin_id: text
        area_1:
          empty: false
          field: area
          id: area_1
          table: views
          plugin_id: text
      header:
        area:
          empty: false
          field: area
          id: area
          table: views
          plugin_id: text
        area_1:
          empty: false
          field: area
          id: area_1
          table: views
          plugin_id: text
      pager:
        type: full
      query:
        type: views_query
      relationships:
        comment_cid:
          field: comment_cid
          id: comment_cid
          table: node_field_data
          plugin_id: standard
        pid:
          field: pid
          id: pid
          table: comment_field_data
          relationship: comment_cid
          plugin_id: standard
        uid:
          field: uid
          id: uid
          table: comment_field_data
          relationship: comment_cid
          plugin_id: standard
      sorts:
        last_comment_name:
          field: last_comment_name
          id: last_comment_name
          table: comment_entity_statistics
          plugin_id: comment_ces_last_comment_name
        last_comment_timestamp:
          field: last_comment_timestamp
          id: last_comment_timestamp
          table: comment_entity_statistics
          plugin_id: date
      style:
        type: default
      row:
        type: fields
      display_extenders: {  }
    display_plugin: default
    display_title: Default
    id: default
    position: 0
  attachment_1:
    display_options:
      displays:
        default: default
        page_1: page_1
      pager:
        type: some
      display_extenders: {  }
    display_plugin: attachment
    display_title: Attachment
    id: attachment_1
    position: 0
  attachment_2:
    display_options:
      displays:
        default: default
        page_1: page_1
      pager:
        type: some
      display_extenders: {  }
    display_plugin: attachment
    display_title: Attachment
    id: attachment_2
    position: 0
  page_1:
    display_options:
      path: test_destroy
      display_extenders: {  }
    display_plugin: page
    display_title: Page
    id: page_1
    position: 0
