langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.default
  module:
    - node
id: test_entity_field_renderered_entity
label: test_entity_field_renderered_entity
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        rendered_entity:
          id: rendered_entity
          table: node
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode: default
      pager:
        type: none
        options:
          offset: 0
      sorts:
        nid:
          id: nid
          table: node_field_data
          field: nid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: nid
          plugin_id: standard
          order: ASC
          expose:
            label: ''
          exposed: false
        title:
          id: title
          table: node_field_data
          field: title
          entity_type: node
          entity_field: title
          plugin_id: standard
      row:
        type: fields
      defaults:
        pager: false
        fields: false
        sorts: false
      rendering_language: '***LANGUAGE_entity_translation***'
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
      tags:
        - 'config:core.entity_view_display.node.article.default'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      rendering_language: '***LANGUAGE_entity_translation***'
      display_extenders: {  }
      path: test_entity_field_renderered_entity/entity_translation
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
      tags:
        - 'config:core.entity_view_display.node.article.default'
  page_2:
    id: page_2
    display_title: Page
    display_plugin: page
    position: 2
    display_options:
      rendering_language: '***LANGUAGE_entity_default***'
      display_extenders: {  }
      path: test_entity_field_renderered_entity/entity_default
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
      tags:
        - 'config:core.entity_view_display.node.article.default'
  page_3:
    id: page_3
    display_title: Page
    display_plugin: page
    position: 3
    display_options:
      rendering_language: '***LANGUAGE_site_default***'
      display_extenders: {  }
      path: test_entity_field_renderered_entity/site_default
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
      tags:
        - 'config:core.entity_view_display.node.article.default'
  page_4:
    id: page_4
    display_title: Page
    display_plugin: page
    position: 4
    display_options:
      rendering_language: '***LANGUAGE_language_interface***'
      display_extenders: {  }
      path: test_entity_field_renderered_entity/language_interface
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
      tags:
        - 'config:core.entity_view_display.node.article.default'
  page_5:
    id: page_5
    display_title: Page
    display_plugin: page
    position: 5
    display_options:
      rendering_language: en
      display_extenders: {  }
      path: test_entity_field_renderered_entity/en
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
      tags:
        - 'config:core.entity_view_display.node.article.default'
  page_6:
    id: page_6
    display_title: Page
    display_plugin: page
    position: 6
    display_options:
      rendering_language: es
      display_extenders: {  }
      path: test_entity_field_renderered_entity/es
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
      tags:
        - 'config:core.entity_view_display.node.article.default'
