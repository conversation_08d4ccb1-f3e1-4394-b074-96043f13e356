<?php

namespace Drupal\views\Plugin\views\display_extender;

use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\views\Plugin\views\PluginBase;

/**
 * @defgroup views_display_extender_plugins Views display extender plugins
 * @{
 * Plugins that offer additional display options across display types.
 *
 * Display extender plugins allow additional options or configuration to be
 * added to views across all display types. For example, if you wanted to allow
 * site users to add certain metadata to the rendered output of every view
 * display regardless of display type, you could provide this option as a
 * display extender.
 *
 * Display extender plugins extend
 * \Drupal\views\Plugin\views\display_extender\DisplayExtenderPluginBase.
 * They must have \Drupal\views\Attribute\ViewsDisplayExtender attributes, and
 * they must be in namespace directory Plugin\views\display_extender.
 *
 * @ingroup views_plugins
 *
 * @see plugin_api
 * @see views_display_plugins
 */

/**
 * Base class for Views display extender plugins.
 */
abstract class DisplayExtenderPluginBase extends PluginBase {

  /**
   * Provide a form to edit options for this plugin.
   */
  public function defineOptionsAlter(&$options) {}

  /**
   * Provide a form to edit options for this plugin.
   */
  public function buildOptionsForm(&$form, FormStateInterface $form_state) {}

  /**
   * Validate the options form.
   */
  public function validateOptionsForm(&$form, FormStateInterface $form_state) {}

  /**
   * Handle any special handling on the validate form.
   */
  public function submitOptionsForm(&$form, FormStateInterface $form_state) {}

  /**
   * Set up any variables on the view prior to execution.
   */
  public function preExecute() {}

  /**
   * Inject anything into the query that the display_extender handler needs.
   */
  public function query() {}

  /**
   * Provide the default summary for options in the views UI.
   *
   * This output is returned as an array.
   */
  public function optionsSummary(&$categories, &$options) {}

  /**
   * Gets a list of defaultable sections and the items they contain.
   */
  public function defaultableSections(&$sections, $section = NULL) {}

}

/**
 * @}
 */
