id: d6_comment_entity_form_display_subject
label: Comment subject form display configuration
migration_tags:
  - Drupal 6
  - Configuration
source:
  plugin: comment_type
  constants:
    entity_type: comment
    field_name: subject
    form_mode: default
    options:
      type: string_textfield
      weight: 10
process:
  entity_type: 'constants/entity_type'
  field_name: 'constants/field_name'
  form_mode: 'constants/form_mode'
  options: 'constants/options'
  bundle:
    -
      plugin: migration_lookup
      source: type
      migration: d6_comment_type
    -
      plugin: skip_on_empty
      method: row
  hidden:
    plugin: static_map
    source: comment_subject_field
    map:
      # If comment_subject_field = FALSE, then hidden = TRUE.
      0: true
      # If comment_subject_field = TRUE, then hidden = FALSE.
      1: false
    default_value: false
destination:
  plugin: component_entity_form_display
migration_dependencies:
  required:
    - d6_comment_type
