<?php

namespace Dr<PERSON>al\node\Plugin\migrate;

use <PERSON><PERSON><PERSON>\migrate\Plugin\Migration;
use Drupal\migrate_drupal\Plugin\MigrationWithFollowUpInterface;

/**
 * Migration plugin for the Drupal 6 node translations.
 */
class D6NodeTranslation extends Migration implements MigrationWithFollowUpInterface {

  /**
   * {@inheritdoc}
   */
  public function generateFollowUpMigrations() {
    $this->migrationPluginManager->clearCachedDefinitions();
    return $this->migrationPluginManager->createInstances('d6_entity_reference_translation');
  }

}
