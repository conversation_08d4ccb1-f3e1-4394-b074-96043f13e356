block_content.add_page:
  path: '/block/add'
  defaults:
    _controller: '\Drupal\block_content\Controller\BlockContentController::add'
    _title: 'Add content block'
  options:
    _admin_route: TRUE
  requirements:
    _entity_create_any_access: 'block_content'

# @todo Deprecate this route once
#   https://www.drupal.org/project/drupal/issues/3159210 is fixed, or remove
#   it in Drupal 11.
# @see https://www.drupal.org/node/3320855
entity.block_content_type.collection.bc:
  path: '/admin/structure/block/block-content/types'
  defaults:
    _controller: '\Drupal\block_content\Controller\BlockContentController::blockContentTypeRedirect'
  options:
    _admin_route: TRUE
  requirements:
    _permission: 'administer block types'

# @todo Deprecate this route once
#   https://www.drupal.org/project/drupal/issues/3159210 is fixed, or remove
#   it in Drupal 11.
# @see https://www.drupal.org/node/3320855
entity.block_content_type.edit_form.bc:
  path: '/admin/structure/block/block-content/manage/{block_content_type}'
  defaults:
    _controller: '\Drupal\block_content\Controller\BlockContentController::blockContentTypeRedirect'
  options:
    _admin_route: TRUE
  requirements:
    _permission: 'administer block types'

block_content.add_form:
  path: '/block/add/{block_content_type}'
  defaults:
    _controller: '\Drupal\block_content\Controller\BlockContentController::addForm'
    _title_callback: '\Drupal\block_content\Controller\BlockContentController::getAddFormTitle'
  options:
    _admin_route: TRUE
  requirements:
    _entity_create_access: 'block_content:{block_content_type}'

entity.block_content.canonical:
  path: '/admin/content/block/{block_content}'
  defaults:
    _entity_form: 'block_content.edit'
    _title_callback: '\Drupal\Core\Entity\Controller\EntityController::title'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'block_content.update'
    block_content: \d+

entity.block_content.edit_form:
  path: '/admin/content/block/{block_content}'
  defaults:
    _entity_form: 'block_content.edit'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'block_content.update'
    block_content: \d+

# @todo Deprecate this route once
#   https://www.drupal.org/project/drupal/issues/3159210 is fixed, or remove
#   it in Drupal 11.
# @see https://www.drupal.org/node/2317981
entity.block_content.edit_form.bc:
  path: '/block/{block_content}'
  defaults:
    _controller: '\Drupal\block_content\Controller\BlockContentController::editRedirect'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'block_content.update'
    block_content: \d+

entity.block_content.delete_form:
  path: '/admin/content/block/{block_content}/delete'
  defaults:
    _entity_form: 'block_content.delete'
    _title: 'Delete'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'block_content.delete'
    block_content: \d+

# @todo Deprecate this route once
#   https://www.drupal.org/project/drupal/issues/3159210 is fixed, or remove
#   it in Drupal 11.
# @see https://www.drupal.org/node/2317981
entity.block_content.delete_form.bc:
  path: '/block/{block_content}/delete'
  defaults:
    _controller: '\Drupal\block_content\Controller\BlockContentController::editRedirect'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'block_content.delete'
    block_content: \d+

block_content.type_add:
  path: '/admin/structure/block-content/add'
  defaults:
    _entity_form: 'block_content_type.add'
    _title: 'Add'
  requirements:
    _entity_create_access: 'block_content_type'

entity.block_content.collection:
  path: '/admin/content/block'
  defaults:
    _title: 'Content blocks'
    _entity_list: 'block_content'
  requirements:
    _permission: 'access block library+administer block content'

# @todo Deprecate this route once
#   https://www.drupal.org/project/drupal/issues/3159210 is fixed, or remove
#   it in Drupal 11.
# @see https://www.drupal.org/node/3320855
entity.block_content.collection.bc:
  path: '/admin/structure/block/block-content'
  defaults:
    _controller: '\Drupal\block_content\Controller\BlockContentController::blockLibraryRedirect'
  requirements:
    _permission: 'access block library+administer block content'
