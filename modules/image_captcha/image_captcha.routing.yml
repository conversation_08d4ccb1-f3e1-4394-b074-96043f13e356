image_captcha.settings:
  path: '/admin/config/people/captcha/image_captcha'
  defaults:
    _form: '\Drupal\image_captcha\Form\ImageCaptchaSettingsForm'
  requirements:
    _permission: 'administer CAPTCHA settings'

image_captcha.font_preview:
  path: '/admin/config/people/captcha/image_captcha/font_preview/{token}'
  defaults:
    _controller: '\Drupal\image_captcha\Controller\CaptchaFontPreviewController::getFont'
  requirements:
    _permission: 'administer CAPTCHA settings'

image_captcha.generator:
  path: '/image-captcha-generate/{session_id}/{timestamp}'
  defaults:
    _controller: '\Drupal\image_captcha\Controller\CaptchaImageGeneratorController::image'
  requirements:
    # Allow access, because anybody is allowed to generate the image captcha:
    _access: 'TRUE'

image_captcha.refresh:
  path: '/image-captcha-refresh/{form_id}'
  defaults:
    _controller: '\Drupal\image_captcha\Controller\CaptchaImageRefreshController::refreshCaptcha'
  requirements:
    # Allow access, because anybody is allowed to refresh the image captcha:
    _access: 'TRUE'
