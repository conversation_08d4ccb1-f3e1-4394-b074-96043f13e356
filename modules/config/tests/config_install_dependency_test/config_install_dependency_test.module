<?php

/**
 * @file
 * Provides hook implementations for testing purposes.
 */

use Drupal\Core\Entity\EntityInterface;

/**
 * Implements hook_ENTITY_TYPE_create().
 */
function config_install_dependency_test_config_test_create(EntityInterface $entity) {
  // Add an enforced dependency on this module so that we can test if this is
  // possible during module installation.
  $entity->setEnforcedDependencies(['module' => ['config_install_dependency_test']]);
}
