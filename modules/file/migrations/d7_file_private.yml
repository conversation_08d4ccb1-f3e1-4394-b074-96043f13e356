id: d7_file_private
label: Private files
audit: true
migration_tags:
  - Drupal 7
  - Content
source:
  plugin: d7_file
  scheme: private
  constants:
    # source_base_path must be set by the tool configuring this migration.
    # It represents the fully qualified path relative to which uris in the files
    # table are specified, and must end with a /. See source_full_path
    # configuration in this migration's process pipeline as an example.
    source_base_path: ''
process:
  # If you are using this file to build a custom migration consider removing
  # the fid field to allow incremental migrations.
  fid: fid
  filename: filename
  source_full_path:
    -
      plugin: concat
      delimiter: /
      source:
        - constants/source_base_path
        - filepath
  uri:
    plugin: file_copy
    source:
      - '@source_full_path'
      - uri
  filemime: filemime
  status: status
  # Drupal 7 didn't keep track of the file's creation or update time -- all it
  # had was the vague "timestamp" column. So we'll use it for both.
  created: timestamp
  changed: timestamp
  uid: uid
destination:
  plugin: entity:file
