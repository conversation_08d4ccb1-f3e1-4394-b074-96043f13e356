uuid,notes,summary,email,emails,checkbox,checkboxes,file,files,likert__q1,likert__q2,likert__q3,composite__title,composite__url,composites,entity_reference,entity_references,not_mapped
e1d59c85-7096-4bee-bafa-1bd6798862e2,valid,valid,<EMAIL>,"<EMAIL>,<EMAIL>,<EMAIL>",1,"two,three,one",https://raw.githubusercontent.com/drupalprojects/webform/8.x-5.x/tests/files/sample.gif,"https://raw.githubusercontent.com/drupalprojects/webform/8.x-5.x/tests/files/sample.gif,https://raw.githubusercontent.com/drupalprojects/webform/8.x-5.x/tests/files/sample.png",3,3,3,Oration<PERSON>,http://example.com,"[{ title: Oratione, url: 'http://example.com' }, { title: Oratione, url: 'http://example.com' }, { title: Oratione, url: 'http://test.com' }]",user_name,user_mail,{not mapped}
9a05b67b-a69a-43d8-a498-9bea83c1cbbe,validation warnings,validation warnings,<EMAIL>,"<EMAIL>,<EMAIL>,<EMAIL>",1,"two,one,three",/webform/plain/tests/files/sample.gif,,3,3,3,Loremipsum,http://test.com,@#$%^not valid ':' yaml,,,{not mapped}
428e338b-d09c-4bb6-8e34-7dcea79f1f0d,validation errors,validation errors,not an email address,"<EMAIL>,<EMAIL>,not an email address",1,invalid,,,1,1,2,Dixisset,http://example.com,"[{ title: Oratione, url: 'http://example.com' }, { title: Oratione, url: 'http://example.com' }, { title: Loremipsum, url: 'http://example.com' }]",,,{not mapped}