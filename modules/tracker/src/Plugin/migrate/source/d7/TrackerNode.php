<?php

namespace Dr<PERSON>al\tracker\Plugin\migrate\source\d7;

use <PERSON>upal\migrate_drupal\Plugin\migrate\source\DrupalSqlBase;

/**
 * Drupal 7 tracker node source from database.
 *
 * For available configuration keys, refer to the parent classes.
 *
 * @see \Drupal\migrate\Plugin\migrate\source\SqlBase
 * @see \Drupal\migrate\Plugin\migrate\source\SourcePluginBase
 *
 * @MigrateSource(
 *   id = "d7_tracker_node",
 *   source_module = "tracker"
 * )
 */
class TrackerNode extends DrupalSqlBase {

  /**
   * {@inheritdoc}
   */
  public function query() {
    return $this->select('tracker_node', 'tn')->fields('tn');
  }

  /**
   * {@inheritdoc}
   */
  public function fields() {
    return [
      'nid' => $this->t('The {node}.nid this record tracks.'),
      'published' => $this->t('Boolean indicating whether the node is published.'),
      'changed' => $this->t('The Unix timestamp when the node was most recently saved or commented on.'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getIds() {
    $ids['nid']['type'] = 'integer';
    return $ids;
  }

}
