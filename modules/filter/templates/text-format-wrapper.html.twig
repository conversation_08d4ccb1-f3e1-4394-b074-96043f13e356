{#
/**
 * @file
 * Default theme implementation for a text format-enabled form element.
 *
 * Available variables:
 * - children: Text format element children.
 * - description: Text format element description.
 * - attributes: HTML attributes for the containing element.
 * - aria_description: Flag for whether or not an ARIA description has been
 *   added to the description container.
 *
 * @see template_preprocess_text_format_wrapper()
 *
 * @ingroup themeable
 */
#}
<div class="js-text-format-wrapper js-form-item form-item">
  {{ children }}
  {% if description %}
    <div{{ attributes }}>{{ description }}</div>
  {% endif %}
</div>
