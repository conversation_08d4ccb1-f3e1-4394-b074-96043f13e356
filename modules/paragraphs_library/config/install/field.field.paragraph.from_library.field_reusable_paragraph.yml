langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_reusable_paragraph
    - paragraphs.paragraphs_type.from_library
id: paragraph.from_library.field_reusable_paragraph
field_name: field_reusable_paragraph
entity_type: paragraph
bundle: from_library
label: 'Reusable paragraph'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraphs_library_item'
  handler_settings:
    target_bundles: null
    sort:
      field: _none
    auto_create: false
field_type: entity_reference
