{"name": "@northernco/ckeditor5-anchor-drupal", "version": "0.5.0", "description": "Drupal CKEditor 5 integration", "keywords": ["ckeditor", "ckeditor5", "ckeditor 5", "ckeditor5-feature", "ckeditor5-plugin", "ckeditor5-dll", "ckeditor5-package-generator"], "main": "src/index.js", "license": "MIT", "engines": {"node": ">=14.0.0", "npm": ">=5.7.1"}, "files": ["lang", "src", "theme", "build", "ckeditor5-metadata.json"], "devDependencies": {"@ckeditor/ckeditor5-autoformat": ">=37.0.1", "@ckeditor/ckeditor5-basic-styles": ">=37.0.1", "@ckeditor/ckeditor5-block-quote": ">=37.0.1", "@ckeditor/ckeditor5-code-block": ">=37.0.1", "@ckeditor/ckeditor5-core": ">=37.0.1", "@ckeditor/ckeditor5-editor-classic": ">=37.0.1", "@ckeditor/ckeditor5-essentials": ">=37.0.1", "@ckeditor/ckeditor5-heading": ">=37.0.1", "@ckeditor/ckeditor5-html-support": ">=37.0.1", "@ckeditor/ckeditor5-image": ">=37.0.1", "@ckeditor/ckeditor5-indent": ">=37.0.1", "@ckeditor/ckeditor5-inspector": ">=4.1.0", "@ckeditor/ckeditor5-link": ">=37.0.1", "@ckeditor/ckeditor5-list": ">=37.0.1", "@ckeditor/ckeditor5-media-embed": ">=37.0.1", "@ckeditor/ckeditor5-package-tools": "^1.0.0-beta.10", "@ckeditor/ckeditor5-paragraph": ">=37.0.1", "@ckeditor/ckeditor5-table": ">=37.0.1", "@ckeditor/ckeditor5-theme-lark": ">=37.0.1", "@ckeditor/ckeditor5-upload": ">=37.0.1", "ckeditor5": ">=37.0.1", "es-toolkit": "^1.39.3", "eslint": "^7.32.0", "eslint-config-ckeditor5": ">=4.4.0", "http-server": "^14.1.0", "husky": "^4.2.5", "lint-staged": "^10.2.6", "stylelint": "^13.13.1", "stylelint-config-ckeditor5": ">=4.4.0"}, "peerDependencies": {"ckeditor5": ">=37.0.1"}, "scripts": {"dll:build": "ckeditor5-package-tools dll:build", "dll:serve": "http-server ./ -o sample/dll.html", "lint": "eslint \"**/*.js\" --quiet", "start": "ckeditor5-package-tools start", "stylelint": "stylelint --quiet --allow-empty-input 'theme/**/*.css'", "test": "ckeditor5-package-tools test", "prepare": "npm run dll:build", "translations:collect": "ckeditor5-package-tools translations:collect", "translations:download": "ckeditor5-package-tools translations:download", "translations:upload": "ckeditor5-package-tools translations:upload"}, "lint-staged": {"**/*.js": ["eslint --quiet"], "**/*.css": ["stylelint --quiet --allow-empty-input"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}