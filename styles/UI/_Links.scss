.cookiesjsr-links {
  list-style: none;
  margin: 0 -.75em;
  padding: 0;
  display: flex;
  flex-direction: column;

  &.links--row {

    @media (min-width: $sm) {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: left;
      align-items: center;
    }

    li {
      margin: 0;
      padding-left: .75em;
      padding-right: .75em;
      position: relative;

      a {
        margin: 0;
        padding: 0;
        line-height: 2em;
        display: inline-block;
      }

      &:first-child:before {
        content: none;
      }


      @media (min-width: $sm) {

        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          width: .3em;
          height: .3em;
          background-color: var(--link-list-separator-color, #FFF);
          transform: translate(-50%, -40%);
        }
      }
    }
  }
}
