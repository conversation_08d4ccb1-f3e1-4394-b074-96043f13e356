!function(t){const a=t.ku=t.ku||{};a.dictionary=Object.assign(a.dictionary||{},{All:"","Almost equal to":"",Angle:"","Approximately equal to":"",Arrows:"","Asterisk operator":"","Austral sign":"","back with leftwards arrow above":"","Bitcoin sign":"",Category:"","Cedi sign":"","Cent sign":"","Colon sign":"","Contains as member":"","Copyright sign":"","Cruzeiro sign":"",Currency:"","Currency sign":"","Degree sign":"","Division sign":"","Dollar sign":"","Dong sign":"","Double dagger":"","Double exclamation mark":"","Double low-9 quotation mark":"","Double question mark":"","downwards arrow to bar":"","downwards dashed arrow":"","downwards double arrow":"","downwards simple arrow":"","Drachma sign":"","Element of":"","Em dash":"","Empty set":"","En dash":"","end with leftwards arrow above":"","Euro sign":"","Euro-currency sign":"","Exclamation question mark":"","For all":"","Fraction slash":"","French franc sign":"","German penny sign":"","Greater-than or equal to":"","Greater-than sign":"","Guarani sign":"","Horizontal ellipsis":"","Hryvnia sign":"","Identical to":"","Indian rupee sign":"",Infinity:"",Integral:"",Intersection:"","Inverted exclamation mark":"","Inverted question mark":"","Kip sign":"",Latin:"","Latin capital letter a with breve":"","Latin capital letter a with macron":"","Latin capital letter a with ogonek":"","Latin capital letter c with acute":"","Latin capital letter c with caron":"","Latin capital letter c with circumflex":"","Latin capital letter c with dot above":"","Latin capital letter d with caron":"","Latin capital letter d with stroke":"","Latin capital letter e with breve":"","Latin capital letter e with caron":"","Latin capital letter e with dot above":"","Latin capital letter e with macron":"","Latin capital letter e with ogonek":"","Latin capital letter eng":"","Latin capital letter g with breve":"","Latin capital letter g with cedilla":"","Latin capital letter g with circumflex":"","Latin capital letter g with dot above":"","Latin capital letter h with circumflex":"","Latin capital letter h with stroke":"","Latin capital letter i with breve":"","Latin capital letter i with dot above":"","Latin capital letter i with macron":"","Latin capital letter i with ogonek":"","Latin capital letter i with tilde":"","Latin capital letter j with circumflex":"","Latin capital letter k with cedilla":"","Latin capital letter l with acute":"","Latin capital letter l with caron":"","Latin capital letter l with cedilla":"","Latin capital letter l with middle dot":"","Latin capital letter l with stroke":"","Latin capital letter n with acute":"","Latin capital letter n with caron":"","Latin capital letter n with cedilla":"","Latin capital letter o with breve":"","Latin capital letter o with double acute":"","Latin capital letter o with macron":"","Latin capital letter r with acute":"","Latin capital letter r with caron":"","Latin capital letter r with cedilla":"","Latin capital letter s with acute":"","Latin capital letter s with caron":"","Latin capital letter s with cedilla":"","Latin capital letter s with circumflex":"","Latin capital letter t with caron":"","Latin capital letter t with cedilla":"","Latin capital letter t with stroke":"","Latin capital letter u with breve":"","Latin capital letter u with double acute":"","Latin capital letter u with macron":"","Latin capital letter u with ogonek":"","Latin capital letter u with ring above":"","Latin capital letter u with tilde":"","Latin capital letter w with circumflex":"","Latin capital letter y with circumflex":"","Latin capital letter y with diaeresis":"","Latin capital letter z with acute":"","Latin capital letter z with caron":"","Latin capital letter z with dot above":"","Latin capital ligature ij":"","Latin capital ligature oe":"","Latin small letter a with breve":"","Latin small letter a with macron":"","Latin small letter a with ogonek":"","Latin small letter c with acute":"","Latin small letter c with caron":"","Latin small letter c with circumflex":"","Latin small letter c with dot above":"","Latin small letter d with caron":"","Latin small letter d with stroke":"","Latin small letter dotless i":"","Latin small letter e with breve":"","Latin small letter e with caron":"","Latin small letter e with dot above":"","Latin small letter e with macron":"","Latin small letter e with ogonek":"","Latin small letter eng":"","Latin small letter f with hook":"","Latin small letter g with breve":"","Latin small letter g with cedilla":"","Latin small letter g with circumflex":"","Latin small letter g with dot above":"","Latin small letter h with circumflex":"","Latin small letter h with stroke":"","Latin small letter i with breve":"","Latin small letter i with macron":"","Latin small letter i with ogonek":"","Latin small letter i with tilde":"","Latin small letter j with circumflex":"","Latin small letter k with cedilla":"","Latin small letter kra":"","Latin small letter l with acute":"","Latin small letter l with caron":"","Latin small letter l with cedilla":"","Latin small letter l with middle dot":"","Latin small letter l with stroke":"","Latin small letter long s":"","Latin small letter n preceded by apostrophe":"","Latin small letter n with acute":"","Latin small letter n with caron":"","Latin small letter n with cedilla":"","Latin small letter o with breve":"","Latin small letter o with double acute":"","Latin small letter o with macron":"","Latin small letter r with acute":"","Latin small letter r with caron":"","Latin small letter r with cedilla":"","Latin small letter s with acute":"","Latin small letter s with caron":"","Latin small letter s with cedilla":"","Latin small letter s with circumflex":"","Latin small letter t with caron":"","Latin small letter t with cedilla":"","Latin small letter t with stroke":"","Latin small letter u with breve":"","Latin small letter u with double acute":"","Latin small letter u with macron":"","Latin small letter u with ogonek":"","Latin small letter u with ring above":"","Latin small letter u with tilde":"","Latin small letter w with circumflex":"","Latin small letter y with circumflex":"","Latin small letter z with acute":"","Latin small letter z with caron":"","Latin small letter z with dot above":"","Latin small ligature ij":"","Latin small ligature oe":"","Left double quotation mark":"","Left single quotation mark":"","Left-pointing double angle quotation mark":"","leftwards arrow to bar":"","leftwards dashed arrow":"","leftwards double arrow":"","leftwards simple arrow":"","Less-than or equal to":"","Less-than sign":"","Lira sign":"","Livre tournois sign":"","Logical and":"","Logical or":"",Macron:"","Manat sign":"",Mathematical:"","Mill sign":"","Minus sign":"","Multiplication sign":"","N-ary product":"","N-ary summation":"",Nabla:"","Naira sign":"","New sheqel sign":"","Nordic mark sign":"","Not an element of":"","Not equal to":"","Not sign":"","on with exclamation mark with left right arrow above":"",Overline:"","Paragraph sign":"","Partial differential":"","Per mille sign":"","Per ten thousand sign":"","Peseta sign":"","Peso sign":"","Plus-minus sign":"","Pound sign":"","Proportional to":"","Question exclamation mark":"","Registered sign":"","Reversed paragraph sign":"","Right double quotation mark":"","Right single quotation mark":"","Right-pointing double angle quotation mark":"","rightwards arrow to bar":"","rightwards dashed arrow":"","rightwards double arrow":"","rightwards simple arrow":"","Ruble sign":"","Rupee sign":"","Section sign":"","Single left-pointing angle quotation mark":"","Single low-9 quotation mark":"","Single right-pointing angle quotation mark":"","soon with rightwards arrow above":"","Special characters":"","Spesmilo sign":"","Square root":"","Tenge sign":"",Text:"","There exists":"","Tilde operator":"","top with upwards arrow above":"","Trade mark sign":"","Tugrik sign":"","Turkish lira sign":"","Two dot leader":"",Union:"","up down arrow with base":"","upwards arrow to bar":"","upwards dashed arrow":"","upwards double arrow":"","upwards simple arrow":"","Vulgar fraction one half":"","Vulgar fraction one quarter":"","Vulgar fraction three quarters":"","Won sign":"","Yen sign":""})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));