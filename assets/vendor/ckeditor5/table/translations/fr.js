!function(e){const l=e.fr=e.fr||{};l.dictionary=Object.assign(l.dictionary||{},{"Align cell text to the bottom":"Aligner le texte en bas","Align cell text to the center":"Aligner la cellule au centre","Align cell text to the left":"Aligner la cellule à gauche","Align cell text to the middle":"Aligner le texte au milieu","Align cell text to the right":"Aligner la cellule à droite","Align cell text to the top":"Aligner le texte en haut","Align table to the left":"Aligner le tableau à gauche","Align table to the right":"Aligner le tableau à droite",Alignment:"Alignement",Background:"Fond",Border:"Bordure","Cell properties":"Propriétés de la cellule","Center table":"Centrer le tableau ","Choose table type":"Choisir le type de tableau",Color:"Couleur",Column:"Colonne","Content table":"Tableau de contenu",Dashed:"Tirets","Delete column":"Supprimer la colonne","Delete row":"Supprimer la ligne",Dimensions:"Dimensions",Dotted:"Pointillés",Double:"Double","Enter table caption":"Saisir la légende du tableau",Groove:"Rainuré","Header column":"Colonne d'entête","Header row":"Ligne d'entête",Height:"Hauteur","Horizontal text alignment toolbar":"Barre d'outils pour modifier l'alignement horizontal du texte","Insert a new table row (when in the last cell of a table)":"Insérer une nouvelle ligne de tableau (dans la dernière cellule d'un tableau)","Insert column left":"Insérer une colonne à gauche","Insert column right":"Insérer une colonne à droite","Insert row above":"Insérer une ligne au-dessus","Insert row below":"Insérer une ligne en-dessous","Insert table":"Insérer un tableau","Insert table layout":"Insérer une mise en page de tableau",Inset:"Relief intérieur","Justify cell text":"Justifier le contenu de la cellule","Keystrokes that can be used in a table cell":"Frappes de touches pouvant être utilisées dans une cellule de tableau","Layout table":"Tableau de mise en page","Merge cell down":"Fusionner la cellule en-dessous","Merge cell left":"Fusionner la cellule à gauche","Merge cell right":"Fusionner la cellule à droite","Merge cell up":"Fusionner la cellule au-dessus","Merge cells":"Fusionner les cellules","Move the selection to the next cell":"Déplacer la sélection vers la cellule suivante","Move the selection to the previous cell":"Déplacer la sélection vers la cellule précédente","Navigate through the table":"Naviguer dans le tableau",None:"Aucun",Outset:"Relief extérieur",Padding:"Remplissage pour aérer le texte",Ridge:"Relief",Row:"Ligne","Select column":"Sélectionner la colonne","Select row":"Sélectionner la ligne",Solid:"Continu","Split cell horizontally":"Scinder la cellule horizontalement","Split cell vertically":"Scinder la cellule verticalement",Style:"Style",Table:"Tableau","Table alignment toolbar":"Barre d'outils pour modifier l'alignement du tableau","Table cell text alignment":"Alignement du texte de la cellule","Table layout":"Mise en page de tableau","Table properties":"Propriétés du tableau","Table toolbar":"Barre d'outils des tableaux","Table type":"Type de tableau","Table type options":"Options de type de tableau",'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".':'La couleur est invalide. Essayez "#FF0000" ou "rgb(255,0,0)" ou "red".','The value is invalid. Try "10px" or "2em" or simply "2".':'La valeur est invalide. Essayez "10px" ou "2em" ou simplement "2".',"Vertical text alignment toolbar":"Barre d'outils pour modifier l'alignement vertical du texte",Width:"Largeur"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));