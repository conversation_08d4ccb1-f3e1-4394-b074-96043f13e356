uuid: 1f486182-e229-4268-bf10-de86bfb402dd
langcode: pl
status: true
dependencies:
  config:
    - field.storage.media.field_media_video_file
    - media.type.video
  module:
    - file
_core:
  default_config_hash: OA1qtcEH8xxQhIPvf4TykZOp1MqSTA1R4HoWxbcWZD0
id: media.video.field_media_video_file
field_name: field_media_video_file
entity_type: media
bundle: video
label: 'Video file'
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: mp4
  max_filesize: ''
  description_field: false
field_type: file
