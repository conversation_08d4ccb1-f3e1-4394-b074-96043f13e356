uuid: 995f9d30-67bc-4e82-a9fc-f553e55d8fb0
langcode: pl
status: true
dependencies:
  config:
    - field.storage.paragraph.field_training_reference
    - node.type.npxtraining
    - paragraphs.paragraphs_type.tile_training
id: paragraph.tile_training.field_training_reference
field_name: field_training_reference
entity_type: paragraph
bundle: tile_training
label: 'Training Reference'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      npxtraining: npxtraining
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
