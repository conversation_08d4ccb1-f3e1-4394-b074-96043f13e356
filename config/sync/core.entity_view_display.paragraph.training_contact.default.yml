uuid: d4d3a93f-e699-42e6-98f9-94f1407989b3
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.training_contact.field_email
    - field.field.paragraph.training_contact.field_name
    - field.field.paragraph.training_contact.field_phone
    - field.field.paragraph.training_contact.field_role
    - field.field.paragraph.training_contact.field_teaser_image
    - field.field.paragraph.training_contact.field_title_above_contacts
    - paragraphs.paragraphs_type.training_contact
  module:
    - entity_reference_revisions
    - image
id: paragraph.training_contact.default
targetEntityType: paragraph
bundle: training_contact
mode: default
content:
  field_email:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  field_name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_phone:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 5
    region: content
  field_role:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 4
    region: content
  field_teaser_image:
    type: image
    label: above
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 2
    region: content
  field_title_above_contacts:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 0
    region: content
hidden: {  }
