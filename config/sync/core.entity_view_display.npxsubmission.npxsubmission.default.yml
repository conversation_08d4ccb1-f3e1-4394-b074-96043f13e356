uuid: b8f06cf4-c09d-471a-9057-870237cde7be
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxsubmission.npxsubmission
    - field.field.npxsubmission.npxsubmission.field_fra_type
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_accept_1
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_accept_2
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_accept_3
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_accept_4
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_address
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_amount
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_bp
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_cb
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_cd
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_cdb
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_t
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_tb
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_td
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_calc_tdb
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_city
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_comment
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_coupon_ref
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_create_class
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_date_nid
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_dcode
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_dis_payment
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_dvalue
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_email
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_firm
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_firstname
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_gen_certs
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_hotel
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_name
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_nid
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_nip
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_online
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_paper
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_participants
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_pdf
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_phone
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_position
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_post_code
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_price_desc
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_private
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_pub_funding
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_send_notify
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_source
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_trainer
    - field.field.npxsubmission.npxsubmission.field_npxsubmission_user_ip
  module:
    - file
    - text
    - user
id: npxsubmission.npxsubmission.default
targetEntityType: npxsubmission
bundle: npxsubmission
mode: default
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 2
    region: content
  field_fra_type:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 44
    region: content
  field_npxsubmission_accept_1:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 20
    region: content
  field_npxsubmission_accept_2:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 21
    region: content
  field_npxsubmission_accept_3:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 22
    region: content
  field_npxsubmission_accept_4:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 23
    region: content
  field_npxsubmission_address:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 8
    region: content
  field_npxsubmission_amount:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 6
    region: content
  field_npxsubmission_calc_bp:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 24
    region: content
  field_npxsubmission_calc_cb:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 25
    region: content
  field_npxsubmission_calc_cd:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 26
    region: content
  field_npxsubmission_calc_cdb:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 27
    region: content
  field_npxsubmission_calc_t:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 28
    region: content
  field_npxsubmission_calc_tb:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 29
    region: content
  field_npxsubmission_calc_td:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 30
    region: content
  field_npxsubmission_calc_tdb:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 31
    region: content
  field_npxsubmission_city:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 9
    region: content
  field_npxsubmission_comment:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 16
    region: content
  field_npxsubmission_coupon_ref:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 40
    region: content
  field_npxsubmission_create_class:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 45
    region: content
  field_npxsubmission_date_nid:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 4
    region: content
  field_npxsubmission_dcode:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 38
    region: content
  field_npxsubmission_dis_payment:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 33
    region: content
  field_npxsubmission_dvalue:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 39
    region: content
  field_npxsubmission_email:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 14
    region: content
  field_npxsubmission_firm:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 7
    region: content
  field_npxsubmission_firstname:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 37
    region: content
  field_npxsubmission_gen_certs:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 47
    region: content
  field_npxsubmission_hotel:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 32
    region: content
  field_npxsubmission_name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 12
    region: content
  field_npxsubmission_nid:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 3
    region: content
  field_npxsubmission_nip:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 11
    region: content
  field_npxsubmission_online:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 42
    region: content
  field_npxsubmission_paper:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 43
    region: content
  field_npxsubmission_participants:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 5
    region: content
  field_npxsubmission_pdf:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 34
    region: content
  field_npxsubmission_phone:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 15
    region: content
  field_npxsubmission_position:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 13
    region: content
  field_npxsubmission_post_code:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 10
    region: content
  field_npxsubmission_price_desc:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 35
    region: content
  field_npxsubmission_private:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 46
    region: content
  field_npxsubmission_pub_funding:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 17
    region: content
  field_npxsubmission_send_notify:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 36
    region: content
  field_npxsubmission_source:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 18
    region: content
  field_npxsubmission_trainer:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 41
    region: content
  field_npxsubmission_user_ip:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 19
    region: content
  title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
  uid:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  changed: true
  id: true
  langcode: true
