uuid: b7afe55c-02f6-48c5-b19b-1d8a72478490
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxlinkdiscount.npxlinkdiscount
    - field.storage.node.field_npxtraining_date_ldis_ref
    - node.type.npxtraining_date
id: node.npxtraining_date.field_npxtraining_date_ldis_ref
field_name: field_npxtraining_date_ldis_ref
entity_type: node
bundle: npxtraining_date
label: 'Rabat z linkiem'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:npxlinkdiscount'
  handler_settings:
    target_bundles:
      npxlinkdiscount: npxlinkdiscount
    sort:
      field: _none
    auto_create: true
    auto_create_bundle: ''
field_type: entity_reference
