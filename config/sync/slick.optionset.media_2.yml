uuid: 4a0161fc-a3db-4e60-af80-79e6026b28fb
langcode: pl
status: false
dependencies:
  enforced:
    module:
      - slick
_core:
  default_config_hash: Zyx4BsKtbNFnRU7WTHAF0DGHMenI0b_Czn4dwl1bQTc
id: media_2
name: media_2
weight: -10
label: 'Media 2'
group: ''
skin: default
breakpoints: 1
optimized: false
options:
  options__active_tab: edit-options-responsives
  settings:
    mobileFirst: false
    asNavFor: ''
    accessibility: true
    adaptiveHeight: true
    autoplay: false
    pauseOnHover: true
    pauseOnDotsHover: false
    autoplaySpeed: 3000
    arrows: true
    prevArrow: Previous
    nextArrow: Next
    downArrow: false
    downArrowTarget: ''
    downArrowOffset: 0
    centerMode: false
    centerPadding: 50px
    dots: true
    dotsClass: slick-dots
    appendDots: $(element)
    draggable: true
    fade: false
    focusOnSelect: false
    infinite: true
    initialSlide: 0
    lazyLoad: ondemand
    mouseWheel: false
    randomize: false
    respondTo: window
    rtl: false
    rows: 1
    slidesPerRow: 1
    slide: ''
    slidesToShow: 2
    slidesToScroll: 1
    speed: 500
    swipe: true
    swipeToSlide: false
    edgeFriction: 0.35
    touchMove: true
    touchThreshold: 5
    useCSS: true
    cssEase: ease
    cssEaseBezier: ''
    cssEaseOverride: ''
    useTransform: true
    easing: linear
    variableWidth: false
    vertical: false
    verticalSwiping: false
    waitForAnimate: true
  responsives:
    responsive:
      -
        breakpoint: 575
        unslick: false
        settings:
          adaptiveHeight: true
          autoplay: false
          pauseOnHover: false
          pauseOnDotsHover: false
          autoplaySpeed: 3000
          arrows: true
          centerMode: false
          centerPadding: 50px
          dots: true
          draggable: true
          fade: false
          focusOnSelect: false
          infinite: false
          initialSlide: 0
          respondTo: window
          rows: 1
          slidesPerRow: 1
          slidesToShow: 1
          slidesToScroll: 1
          speed: 500
          swipe: false
          swipeToSlide: false
          edgeFriction: 0.35
          touchMove: true
          touchThreshold: 5
          variableWidth: false
          vertical: false
          verticalSwiping: false
          waitForAnimate: true
