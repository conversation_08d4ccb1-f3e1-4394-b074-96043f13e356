entity.imageapi_optimize_pipeline.collection:
  path: '/admin/config/media/imageapi-optimize-pipelines'
  defaults:
    _entity_list: 'imageapi_optimize_pipeline'
    _title: 'Image Optimize pipelines'
  requirements:
    _permission: 'administer imageapi optimize pipelines'

imageapi_optimize.pipeline_add:
  path: '/admin/config/media/imageapi-optimize-pipelines/add'
  defaults:
    _entity_form: imageapi_optimize_pipeline.add
    _title: 'Add Image Optimize pipeline'
  requirements:
    _permission: 'administer imageapi optimize pipelines'

entity.imageapi_optimize_pipeline.edit_form:
  path: '/admin/config/media/imageapi-optimize-pipelines/manage/{imageapi_optimize_pipeline}'
  defaults:
    _entity_form: imageapi_optimize_pipeline.edit
    _title: 'Edit pipeline'
  requirements:
    _permission: 'administer imageapi optimize pipelines'

entity.imageapi_optimize_pipeline.delete_form:
  path: '/admin/config/media/imageapi-optimize-pipelines/manage/{imageapi_optimize_pipeline}/delete'
  defaults:
    _entity_form: 'imageapi_optimize_pipeline.delete'
    _title: 'Delete'
  requirements:
    _permission: 'administer imageapi optimize pipelines'

entity.imageapi_optimize_pipeline.flush_form:
  path: '/admin/config/media/imageapi-optimize-pipelines/manage/{imageapi_optimize_pipeline}/flush'
  defaults:
    _entity_form: 'imageapi_optimize_pipeline.flush'
    _title: 'Flush'
  requirements:
    _permission: 'administer imageapi optimize pipelines'

imageapi_optimize.processor_delete:
  path: '/admin/config/media/imageapi-optimize-pipelines/manage/{imageapi_optimize_pipeline}/processors/{imageapi_optimize_processor}/delete'
  defaults:
    _form: '\Drupal\imageapi_optimize\Form\ImageAPIOptimizeProcessorDeleteForm'
    _title: 'Delete Image Optimize processor'
  requirements:
    _permission: 'administer imageapi optimize pipelines'

imageapi_optimize.processor_add_form:
  path: '/admin/config/media/imageapi-optimize-pipelines/manage/{imageapi_optimize_pipeline}/add/{imageapi_optimize_processor}'
  defaults:
    _form: '\Drupal\imageapi_optimize\Form\ImageAPIOptimizeProcessorAddForm'
    _title: 'Add Image Optimize processor'
  requirements:
    _permission: 'administer imageapi optimize pipelines'

imageapi_optimize.processor_edit_form:
  path: '/admin/config/media/imageapi-optimize-pipelines/manage/{imageapi_optimize_pipeline}/processors/{imageapi_optimize_processor}'
  defaults:
    _form: '\Drupal\imageapi_optimize\Form\ImageAPIOptimizeProcessorEditForm'
    _title: 'Edit Image Optimizer processor'
  requirements:
    _permission: 'administer imageapi optimize pipelines'
