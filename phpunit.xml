<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         colors="true"
         bootstrap="../../../tests/bootstrap.php"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutChangesToGlobalState="true"
         failOnWarning="true"
         printerClass="\Drupal\Tests\Listeners\HtmlOutputPrinter"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd">
  <php>
    <!-- Set error reporting to E_ALL. -->
    <ini name="error_reporting" value="32767"/>
    <!-- Do not limit the amount of memory tests take to run. -->
    <ini name="memory_limit" value="-1"/>
  </php>
  <listeners>
    <listener class="\Drupal\Tests\Listeners\DrupalListener">
    </listener>
  </listeners>
  <!-- Settings for coverage reports. -->
  <coverage processUncoveredFiles="true">
    <include>
      <directory suffix=".php">./</directory>
      <directory suffix=".module">./</directory>
      <directory suffix=".install">./</directory>
      <directory suffix=".inc">./</directory>
    </include>
    <exclude>
      <directory>./tests</directory>
    </exclude>
  </coverage>
</phpunit>
