{#
/**
 * @file
 * Theme implementation for text filter help.
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - children: The rendered child elements of the container.
 *
 * @see template_preprocess_container()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    has_parent ? 'js-form-wrapper',
    has_parent ? 'form-wrapper',
    'filter-help',
  ]
%}
<div{{ attributes.addClass(classes) }}>{{ children }}</div>
