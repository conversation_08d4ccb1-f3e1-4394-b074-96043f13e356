{#
/**
 * @file
 * Theme override to display a list of forums and containers.
 *
 * Available variables:
 * - forums: A collection of forums and containers to display. It is keyed to
 *   the numeric IDs of all child forums and containers. Each forum in forums
 *   contains:
 *   - is_container: A flag indicating if the forum can contain other
 *     forums. Otherwise, the forum can only contain topics.
 *   - depth: How deep the forum is in the current hierarchy.
 *   - zebra: 'even' or 'odd', used for row class.
 *   - icon_class: 'default' or 'new', used for forum icon class.
 *   - icon_title: Text alternative for the forum icon.
 *   - name: The name of the forum.
 *   - link: The URL to link to this forum.
 *   - description: The description field for the forum, containing:
 *     - value: The descriptive text for the forum.
 *   - new_topics: A flag indicating if the forum contains unread posts.
 *   - new_url: A URL to the forum's unread posts.
 *   - new_text: Text for the above URL, which tells how many new posts.
 *   - old_topics: A count of posts that have already been read.
 *   - num_posts: The total number of posts in the forum.
 *   - last_reply: Text representing the last time a forum was posted or
 *     commented in.
 * - forum_id: Forum ID for the current forum. Parent to all items within the
 *   forums array.
 *
 * @see template_preprocess_forum_list()
 */
#}
<table id="forum-{{ forum_id }}">
  <thead>
    <tr>
      <th>{{ 'Forum'|t }}</th>
      <th>{{ 'Topics'|t }}</th>
      <th>{{ 'Posts'|t }}</th>
      <th>{{ 'Last post'|t }}</th>
    </tr>
  </thead>
  <tbody>
  {% for child_id, forum in forums %}
    <tr id="forum-list-{{ child_id }}" class="{{ forum.zebra }}">
      <td {% if forum.is_container == true -%}
        colspan="4" class="container"
      {%- else -%}
        class="forum-list__forum"
      {%- endif -%}>
        {#
          Enclose the contents of this cell with X divs, where X is the
          depth this forum resides at. This will allow us to use CSS
          left-margin for indenting.
        #}
        {% if forum.depth > 0 %}{% for i in 1..forum.depth %}<div class="indented">{% endfor %}{% endif %}
          <div class="forum__icon forum-status-{{ forum.icon_class }}" title="{{ forum.icon_title }}">
            <span class="visually-hidden">{{ forum.icon_title }}</span>
          </div>
          <div class="forum__name"><a href="{{ forum.link }} " class="forum__name--link">{{ forum.label }}</a></div>
          {% if forum.description.value %}
            <div class="forum__description">{{ forum.description.value }}</div>
          {% endif %}
        {% if forum.depth > 0 %}{% for i in 1..forum.depth %}</div>{% endfor %}{% endif %}
      </td>
      {% if forum.is_container == false %}
        <td class="forum__topics">
          {{ forum.num_topics }}
          {% if forum.new_topics == true %}
            <br />
            <a href="{{ forum.new_url }}">{{ forum.new_text }}</a>
          {% endif %}
        </td>
        <td class="forum__posts">{{ forum.num_posts }}</td>
        <td class="forum__last-reply">{{ forum.last_reply }}</td>
      {% endif %}
    </tr>
  {% endfor %}
  </tbody>
</table>
