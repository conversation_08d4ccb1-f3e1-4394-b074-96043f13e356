{#
/**
 * @file
 * Default theme implementation for an audio file element.
 *
 * Available variables:
 * - element: The element.
 * - value: The element's value.
 * - options Associative array of options for element.
 * - file: The element's File object.
 * - file_link: Link to the file.
 *
 * @see http://caniuse.com/#feat=audio
 * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/audio
 */
#}
{% if extension == 'mp3' %}
  <div class="webform-audio-file">
    <audio controls>
      <source src="{{ uri }}" type="{{ type }}">
    </audio>
  </div>
{% endif %}
<div>{{ file_link }}</div>
