{#
/**
 * @file
 *
 * Default template for payment methods.
 *
 * Available variables:
 * - attributes: HTML attributes for the wrapper.
 * - payment_method: The rendered payment_method fields.
 *   Use 'payment_method' to print them all, or print a subset such as
 *   'payment_method.label'. Use the following code to exclude the
 *   printing of a given field:
 *   @code
 *   {{ payment_method|without('label') }}
 *   @endcode
 * - payment_method_entity: The payment_method entity.
 *
 * @ingroup themeable
 */
#}
<article{{ attributes }}>
  <div class="field field--name-label">
    {{ payment_method.label }}
  </div>
  {% if payment_method_entity.isReusable and payment_method_entity.expiresTime %}
    <div class="field field--name-expires">
      {{ 'Expires'|t }} {{ payment_method_entity.expiresTime|format_date('custom', 'n/Y') }}
    </div>
  {% endif %}
  {{ payment_method.billing_profile }}
</article>
