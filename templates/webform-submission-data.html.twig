{#
/**
 * @file
 * Default theme implementation for webform submission data.
 *
 * Available variables:
 * - webform_submission: The webform submission.
 * - webform: The webform.
 *
 * @see template_preprocess_webform_submission_data()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'webform-submission-data',
    'webform-submission-data--webform-' ~ webform.id()|clean_class,
    view_mode ? 'webform-submission-data--view-mode-' ~ view_mode|clean_class,
  ]
%}
<div{{ attributes.addClass(classes) }}>
{{ elements }}
</div>
